#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试连续 HIGH 点的场景
通过手动设置 ZigZag 点来模拟问题
"""

from datetime import datetime, timedelta
from decimal import Decimal
from zigzag_analysis import StockData
from stock_visualization import StockVisualizer

def create_data_with_consecutive_highs():
    """创建包含连续 HIGH 点的数据"""
    base_date = datetime(2024, 3, 1)
    stock_data = []
    
    # 创建基础数据
    prices = [
        (0, 7.0, 7.1, 6.9, 7.05),   # 3/1
        (1, 7.05, 7.2, 7.0, 7.15),  # 3/2
        (2, 7.15, 7.3, 7.1, 7.25),  # 3/3
        (3, 7.25, 7.4, 7.2, 7.35),  # 3/4
        (4, 7.35, 7.5, 7.3, 7.45),  # 3/5
        (5, 7.45, 7.6, 7.4, 7.55),  # 3/6
        (6, 7.55, 7.7, 7.5, 7.65),  # 3/7
        (7, 7.65, 7.8, 7.6, 7.75),  # 3/8
        (8, 7.75, 7.9, 7.7, 7.85),  # 3/9
        (9, 7.85, 8.0, 7.8, 7.95),  # 3/10
        (10, 7.95, 8.1, 7.9, 8.05), # 3/11
        (11, 8.05, 8.2, 8.0, 8.15), # 3/12 - 将被设为第一个 HIGH
        (12, 8.15, 8.1, 7.95, 8.0), # 3/13
        (13, 8.0, 8.05, 7.9, 7.95), # 3/14
        (14, 7.95, 8.0, 7.85, 7.9), # 3/15 - 将被设为 LOW
        (15, 7.9, 7.95, 7.85, 7.92), # 3/16
        (16, 7.92, 8.0, 7.9, 7.98),  # 3/17
        (17, 7.98, 8.1, 7.95, 8.05), # 3/18
        (18, 8.05, 8.2, 8.0, 8.15),  # 3/19
        (19, 8.15, 8.3, 8.1, 8.25),  # 3/20
        (20, 8.25, 8.4, 8.2, 8.35),  # 3/21
        (21, 8.35, 8.5, 8.3, 8.45),  # 3/22
        (22, 8.45, 8.6, 8.4, 8.55),  # 3/23
        (23, 8.55, 8.7, 8.5, 8.65),  # 3/24
        (24, 8.65, 8.8, 8.6, 8.75),  # 3/25
        (25, 8.75, 8.9, 8.7, 8.85),  # 3/26
        (26, 8.85, 9.0, 8.8, 8.95),  # 3/27
        (27, 8.95, 9.1, 8.9, 9.05),  # 3/28
        (28, 9.05, 9.2, 9.0, 9.15),  # 3/29
        (29, 9.15, 9.3, 9.1, 9.25),  # 3/30
        (30, 9.25, 9.4, 9.2, 9.35),  # 3/31
        (31, 9.35, 9.5, 9.3, 9.45),  # 4/1
        (32, 9.45, 9.6, 9.4, 9.55),  # 4/2
        (33, 9.55, 9.7, 9.5, 9.65),  # 4/3
        (34, 9.65, 9.8, 9.6, 9.75),  # 4/4
        (35, 9.75, 9.9, 9.7, 9.85),  # 4/5
        (36, 9.85, 10.0, 9.8, 9.95), # 4/6
        (37, 9.95, 10.1, 9.9, 10.05),# 4/7
        (38, 10.05, 10.2, 10.0, 10.15), # 4/8
        (39, 10.15, 10.3, 10.1, 10.25), # 4/9
        (40, 10.25, 10.4, 10.2, 10.35), # 4/10 - 将被设为第二个 HIGH
        (41, 10.35, 10.3, 9.8, 9.9),  # 4/11
        (42, 9.9, 9.95, 9.5, 9.6),    # 4/12
    ]
    
    for day_offset, open_price, high_price, low_price, close_price in prices:
        date = base_date + timedelta(days=day_offset)
        stock_data.append(StockData(
            symbol="TEST_CONSECUTIVE",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        ))
    
    # 手动设置 ZigZag 点来模拟连续 HIGH 的问题
    # 3/12 - 第一个 HIGH
    stock_data[11].is_zigzag_point = True
    stock_data[11].zigzag_point_type = 'HIGH'
    
    # 3/15 - LOW (正常)
    stock_data[14].is_zigzag_point = True
    stock_data[14].zigzag_point_type = 'LOW'
    
    # 4/10 - 第二个 HIGH (这会创造一个 LOW -> HIGH -> HIGH 的序列，缺少中间的 LOW)
    stock_data[40].is_zigzag_point = True
    stock_data[40].zigzag_point_type = 'HIGH'
    
    # 为了创造连续的 HIGH，我们再添加一个 HIGH
    # 让我们假设算法错误地把 4/8 也标记为 HIGH
    stock_data[38].is_zigzag_point = True
    stock_data[38].zigzag_point_type = 'HIGH'
    
    return stock_data

def test_consecutive_highs_visualization():
    """测试连续 HIGH 点的可视化"""
    print("=" * 80)
    print("测试连续 HIGH 点的可视化")
    print("=" * 80)
    
    # 创建包含连续 HIGH 点的数据
    stock_data = create_data_with_consecutive_highs()
    print(f"创建了 {len(stock_data)} 个数据点")
    
    # 显示手动设置的 ZigZag 点
    zigzag_points = [data for data in stock_data if data.is_zigzag_point]
    print(f"\n手动设置的 ZigZag 点 ({len(zigzag_points)} 个):")
    print("-" * 80)
    
    for i, data in enumerate(zigzag_points):
        price = float(data.high if data.zigzag_point_type == 'HIGH' else data.low)
        print(f"{i+1:2d}. {data.date.strftime('%Y-%m-%d')} | {data.zigzag_point_type:4s} | "
              f"价格: {price:6.2f} | 收盘: {float(data.close):6.2f}")
    
    # 检查是否有连续的同类型点
    consecutive_found = False
    for i in range(len(zigzag_points) - 1):
        if zigzag_points[i].zigzag_point_type == zigzag_points[i+1].zigzag_point_type:
            consecutive_found = True
            print(f"\n⚠️  发现连续的 {zigzag_points[i].zigzag_point_type} 点:")
            print(f"   {zigzag_points[i].date.strftime('%Y-%m-%d')} -> {zigzag_points[i+1].date.strftime('%Y-%m-%d')}")
    
    if not consecutive_found:
        print("\n✅ 没有发现连续的同类型点")
    
    # 创建可视化
    visualizer = StockVisualizer()
    output_file = "test_consecutive_highs_validation.html"
    
    filename = visualizer.save_chart(stock_data, output_file, "TEST_CONSECUTIVE")
    
    print(f"\n✅ 可视化已生成: {filename}")
    print("📊 请打开 HTML 文件查看:")
    print(f"   - 右上角应该显示黄色警告面板（如果检测到连续点）")
    print(f"   - 面板会显示具体的问题详情和建议的缺失点")
    
    return filename

if __name__ == "__main__":
    test_consecutive_highs_visualization()
