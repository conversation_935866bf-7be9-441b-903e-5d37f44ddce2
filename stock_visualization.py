import pandas as pd
import json
from pyecharts import options as opts
from pyecharts.charts import K<PERSON>, Line, Bar, Grid, Scatter
from pyecharts.commons.utils import JsCode
from typing import List, Dict, Any
from decimal import Decimal
from zigzag_analysis import StockData, ZigZagAnalyzer, get_stock_data, dataframe_to_stock_data


class StockVisualizer:
    """股票数据可视化类，使用pyecharts创建专业的K线图表"""
    
    def __init__(self, width: str = "100%", height: str = "800px"):
        self.width = width
        self.height = height
        self.colors = {
            'up': '#ec0000',      # 上涨红色
            'down': '#00da3c',    # 下跌绿色
            'volume_up': '#ec000040',   # 成交量上涨（透明红色）
            'volume_down': '#00da3c40', # 成交量下跌（透明绿色）
            'zigzag_high': '#ff6b6b',   # 峰点颜色
            'zigzag_low': '#4ecdc4',    # 谷点颜色
        }
    
    def _prepare_kline_data(self, stock_data: List[StockData]) -> List[List]:
        """准备K线数据"""
        kline_data = []
        for data in stock_data:
            kline_data.append([
                float(data.open),
                float(data.close),
                float(data.low),
                float(data.high)
            ])
        return kline_data
    
    def _prepare_volume_data(self, stock_data: List[StockData]) -> List[Dict]:
        """准备成交量数据"""
        volume_data = []
        for data in stock_data:
            # 根据涨跌设置颜色
            color = self.colors['volume_up'] if data.close >= data.open else self.colors['volume_down']
            volume_data.append({
                'value': data.volume,
                'itemStyle': {'color': color}
            })
        return volume_data
    
    def _prepare_zigzag_data(self, stock_data: List[StockData]) -> Dict[str, List]:
        """准备ZigZag峰谷点数据"""
        zigzag_highs = []
        zigzag_lows = []
        
        for i, data in enumerate(stock_data):
            if data.is_zigzag_point:
                if data.zigzag_point_type == 'HIGH':
                    zigzag_highs.append([i, float(data.high)])
                elif data.zigzag_point_type == 'LOW':
                    zigzag_lows.append([i, float(data.low)])
        
        return {
            'highs': zigzag_highs,
            'lows': zigzag_lows
        }
    
    def _prepare_dates(self, stock_data: List[StockData]) -> List[str]:
        """准备日期数据"""
        return [data.date.strftime('%Y-%m-%d') for data in stock_data]

    def _create_zigzag_line(self, stock_data: List[StockData]) -> List:
        """创建ZigZag连线数据"""
        zigzag_line = [None] * len(stock_data)

        for i, data in enumerate(stock_data):
            if data.is_zigzag_point:
                if data.zigzag_point_type == 'HIGH':
                    zigzag_line[i] = float(data.high)
                elif data.zigzag_point_type == 'LOW':
                    zigzag_line[i] = float(data.low)

        return zigzag_line
    
    def _create_kline_chart(self, stock_data: List[StockData]) -> Kline:
        """创建K线图"""
        dates = self._prepare_dates(stock_data)
        kline_data = self._prepare_kline_data(stock_data)
        zigzag_data = self._prepare_zigzag_data(stock_data)
        
        # 创建K线图
        kline = (
            Kline()
            .add_xaxis(dates)
            .add_yaxis(
                series_name="K线",
                y_axis=kline_data,
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.colors['up'],
                    color0=self.colors['down'],
                    border_color=self.colors['up'],
                    border_color0=self.colors['down'],
                )
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(title="股票K线图", pos_left="0"),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    is_scale=True,
                    boundary_gap=False,
                    axisline_opts=opts.AxisLineOpts(is_on_zero=False),
                    splitline_opts=opts.SplitLineOpts(is_show=False),
                    split_number=20,
                    min_="dataMin",
                    max_="dataMax",
                ),
                yaxis_opts=opts.AxisOpts(
                    is_scale=True,
                    splitline_opts=opts.SplitLineOpts(is_show=True)
                ),
                tooltip_opts=opts.TooltipOpts(
                    trigger="axis",
                    axis_pointer_type="cross",
                    background_color="rgba(245, 245, 245, 0.95)",
                    border_width=1,
                    border_color="#ccc",
                    textstyle_opts=opts.TextStyleOpts(color="#000", font_size=12),
                    formatter=JsCode("""
                        function (params) {
                            var k = null;
                            for (var i = 0; i < params.length; i++) {
                                if (params[i].seriesType === 'candlestick') { k = params[i]; break; }
                            }
                            if (!k && params.length) { k = params[0]; }
                            if (!k) { return ''; }
                            var idx = k.dataIndex || 0;
                            var sdArr = window.stockDataForTooltip || [];
                            var sd = sdArr[idx] || {};
                            var v = Array.isArray(k.value) ? k.value : [];
                            function num(n){ return (n == null || isNaN(n)) ? '-' : Number(n).toFixed(2); }
                            var open = num(v[1]);
                            var close = num(v[2]);
                            var low = num(v[3]);
                            var high = num(v[4]);
                            var parts = [];
                            parts.push('日期: ' + (k.axisValue || ''));
                            parts.push('开盘: ' + open);
                            parts.push('收盘: ' + close);
                            parts.push('最低: ' + low);
                            parts.push('最高: ' + high);
                            parts.push('成交量: ' + (sd.volume != null ? Number(sd.volume).toLocaleString() : '-'));
                            if (sd.is_zigzag_point) { parts.push('峰谷点: ' + (sd.zigzag_point_type || '')); }
                            parts.push('趋势状态: ' + (sd.trend_status || '暂无'));
                            return parts.join('<br/>');
                        }
                    """)
                ),
                datazoom_opts=[
                    opts.DataZoomOpts(
                        is_show=False,
                        type_="inside",
                        xaxis_index=[0, 1],
                        range_start=0,
                        range_end=100,
                    ),
                    opts.DataZoomOpts(
                        is_show=True,
                        xaxis_index=[0, 1],
                        type_="slider",
                        pos_top="90%",
                        range_start=0,
                        range_end=100,
                    ),
                ],
                # 添加十字线
                axispointer_opts=opts.AxisPointerOpts(
                    is_show=True,
                    link=[{"xAxisIndex": "all"}],
                    label=opts.LabelOpts(background_color="#777"),
                ),
            )
        )
        
        # 添加ZigZag连线（可选）
        zigzag_line_data = self._create_zigzag_line(stock_data)
        if zigzag_line_data:
            zigzag_line = (
                Line()
                .add_xaxis(dates)
                .add_yaxis(
                    series_name="ZigZag趋势线",
                    y_axis=zigzag_line_data,
                    is_connect_nones=False,
                    linestyle_opts=opts.LineStyleOpts(width=1, color="#1890ff", type_="dashed"),
                    symbol="none",
                    label_opts=opts.LabelOpts(is_show=False)
                )
            )
            kline.overlap(zigzag_line)

        # 用 Scatter 标注峰点 + 用 Line 连接相邻峰点
        if zigzag_data['highs']:
            highs_y = [None] * len(dates)
            for idx, price in zigzag_data['highs']:
                highs_y[idx] = price
            # 峰点散点
            peak_scatter = (
                Scatter()
                .add_xaxis(dates)
                .add_yaxis(
                    series_name="峰点",
                    y_axis=highs_y,
                    symbol="triangle",
                    symbol_size=14,
                    itemstyle_opts=opts.ItemStyleOpts(color=self.colors['zigzag_high'], border_color="#ffffff", border_width=1),
                    label_opts=opts.LabelOpts(is_show=False)
                )
                .set_series_opts()
            )
            kline.overlap(peak_scatter)
            # 峰点连线（连接相邻峰点）
            peak_line_only = (
                Line()
                .add_xaxis(dates)
                .add_yaxis(
                    series_name="峰点连线",
                    y_axis=highs_y,
                    is_connect_nones=True,
                    is_smooth=False,
                    symbol="none",
                    linestyle_opts=opts.LineStyleOpts(color=self.colors['zigzag_high'], width=1.5),
                    label_opts=opts.LabelOpts(is_show=False)
                )
            )
            kline.overlap(peak_line_only)

        # 用 Scatter 标注谷点 + 用 Line 连接相邻谷点
        if zigzag_data['lows']:
            lows_y = [None] * len(dates)
            for idx, price in zigzag_data['lows']:
                lows_y[idx] = price
            # 谷点散点
            valley_scatter = (
                Scatter()
                .add_xaxis(dates)
                .add_yaxis(
                    series_name="谷点",
                    y_axis=lows_y,
                    symbol="diamond",
                    symbol_size=14,
                    itemstyle_opts=opts.ItemStyleOpts(color=self.colors['zigzag_low'], border_color="#ffffff", border_width=1),
                    label_opts=opts.LabelOpts(is_show=False)
                )
                .set_series_opts()
            )
            kline.overlap(valley_scatter)
            # 谷点连线（连接相邻谷点）
            valley_line_only = (
                Line()
                .add_xaxis(dates)
                .add_yaxis(
                    series_name="谷点连线",
                    y_axis=lows_y,
                    is_connect_nones=True,
                    is_smooth=False,
                    symbol="none",
                    linestyle_opts=opts.LineStyleOpts(color=self.colors['zigzag_low'], width=1.5),
                    label_opts=opts.LabelOpts(is_show=False)
                )
            )
            kline.overlap(valley_line_only)
        
        return kline
    
    def _create_volume_chart(self, stock_data: List[StockData]) -> Bar:
        """创建成交量图，并叠加5日/20日成交量均线"""
        dates = self._prepare_dates(stock_data)
        volume_data = self._prepare_volume_data(stock_data)

        # 成交量柱状图
        volume_chart = (
            Bar()
            .add_xaxis(dates)
            .add_yaxis(
                series_name="成交量",
                y_axis=volume_data,
                xaxis_index=1,
                yaxis_index=1,
                label_opts=opts.LabelOpts(is_show=False),
                category_gap="20%",
            )
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    grid_index=1,
                    axislabel_opts=opts.LabelOpts(is_show=False),
                ),
                yaxis_opts=opts.AxisOpts(
                    grid_index=1,
                    split_number=3,
                    axisline_opts=opts.AxisLineOpts(is_on_zero=False),
                    axistick_opts=opts.AxisTickOpts(is_show=False),
                    splitline_opts=opts.SplitLineOpts(is_show=False),
                    axislabel_opts=opts.LabelOpts(is_show=True),
                ),
                legend_opts=opts.LegendOpts(is_show=True),
                tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="line"),
                axispointer_opts=opts.AxisPointerOpts(is_show=True, link=[{"xAxisIndex": "all"}], label=opts.LabelOpts(background_color="#777")),
            )
        )

        # 叠加5日/20日成交量均线
        vol5 = [float(d.vol5) if d.vol5 is not None else None for d in stock_data]
        vol20 = [float(d.vol20) if d.vol20 is not None else None for d in stock_data]

        vol5_line = (
            Line()
            .add_xaxis(dates)
            .add_yaxis(
                series_name="VOL5",
                y_axis=vol5,
                xaxis_index=1,
                yaxis_index=1,
                is_smooth=True,
                linestyle_opts=opts.LineStyleOpts(color="#ff9900", width=1.5),
                label_opts=opts.LabelOpts(is_show=False),
                is_connect_nones=True,
            )
        )

        vol20_line = (
            Line()
            .add_xaxis(dates)
            .add_yaxis(
                series_name="VOL20",
                y_axis=vol20,
                xaxis_index=1,
                yaxis_index=1,
                is_smooth=True,
                linestyle_opts=opts.LineStyleOpts(color="#3b8cff", width=1.5),
                label_opts=opts.LabelOpts(is_show=False),
                is_connect_nones=True,
            )
        )

        volume_chart.overlap(vol5_line)
        volume_chart.overlap(vol20_line)

        return volume_chart
    
    def create_comprehensive_chart(self, stock_data: List[StockData], symbol: str = "") -> Grid:
        """创建综合图表（K线+成交量）"""
        # 创建K线图和成交量图
        kline_chart = self._create_kline_chart(stock_data)
        volume_chart = self._create_volume_chart(stock_data)
        
        # 创建网格布局
        grid = (
            Grid(init_opts=opts.InitOpts(width=self.width, height=self.height))
            .add(
                kline_chart,
                grid_opts=opts.GridOpts(pos_left="10%", pos_right="8%", height="60%"),
            )
            .add(
                volume_chart,
                grid_opts=opts.GridOpts(
                    pos_left="10%", pos_right="8%", pos_top="70%", height="16%"
                ),
            )
        )
        
        # 添加JavaScript代码来支持tooltip中的趋势状态显示
        stock_data_js = []
        for data in stock_data:
            stock_data_js.append({
                'trend_status': data.trend_status or '暂无'
            })
        
        # 将数据注入到页面中，包含更多信息
        enhanced_stock_data = []
        for d in stock_data:
            enhanced_stock_data.append({
                'trend_status': d.trend_status or '暂无',
                'is_zigzag_point': bool(d.is_zigzag_point),
                'zigzag_point_type': d.zigzag_point_type,
                'volume': int(d.volume)
            })

        grid.add_js_funcs("window.stockDataForTooltip = {}".format(json.dumps(enhanced_stock_data, ensure_ascii=False)))
        
        return grid
    
    def save_chart(self, stock_data: List[StockData], filename: str = "stock_chart.html", symbol: str = ""):
        """保存图表到HTML文件"""
        chart = self.create_comprehensive_chart(stock_data, symbol)
        chart.render(filename)
        print(f"图表已保存到: {filename}")
        return filename


def visualize_stock_analysis(stock_code: str, start_date: str, end_date: str, 
                           base_threshold: float = 0.05, 
                           output_file: str = None) -> str:
    """
    完整的股票分析和可视化流程
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期 (YYYYMMDD)
        end_date: 结束日期 (YYYYMMDD)
        base_threshold: ZigZag基础阈值
        output_file: 输出文件名
    
    Returns:
        生成的HTML文件路径
    """
    print(f"正在获取 {stock_code} 的数据...")
    
    # 获取股票数据
    df = get_stock_data(stock_code, start_date, end_date)
    stock_data_list = dataframe_to_stock_data(df, stock_code)
    
    print("正在进行ZigZag分析...")
    
    # 进行ZigZag分析
    analyzer = ZigZagAnalyzer(
        base_threshold=base_threshold,
        dynamic_threshold=True,
        trend_lookback_points=4
    )
    
    analyzed_stock_data = analyzer.calculate_and_analyze(stock_data_list)
    
    print("正在生成可视化图表...")
    
    # 创建可视化
    visualizer = StockVisualizer()
    
    if output_file is None:
        output_file = f"{stock_code}_analysis_{start_date}_{end_date}.html"
    
    filename = visualizer.save_chart(analyzed_stock_data, output_file, stock_code)
    
    # 打印一些统计信息
    zigzag_points = [data for data in analyzed_stock_data if data.is_zigzag_point]
    print(f"\n分析完成！")
    print(f"数据期间: {start_date} - {end_date}")
    print(f"总交易日: {len(analyzed_stock_data)}")
    print(f"识别的峰谷点: {len(zigzag_points)}")
    print(f"峰点数量: {len([p for p in zigzag_points if p.zigzag_point_type == 'HIGH'])}")
    print(f"谷点数量: {len([p for p in zigzag_points if p.zigzag_point_type == 'LOW'])}")
    
    return filename


if __name__ == "__main__":
    # 示例使用
    html_file = visualize_stock_analysis(
        stock_code="600000",
        start_date="20230101", 
        end_date="20240101",
        base_threshold=0.05,
        output_file="浦发银行_zigzag_analysis.html"
    )
    
    print(f"\n可视化完成！请打开文件查看: {html_file}")
