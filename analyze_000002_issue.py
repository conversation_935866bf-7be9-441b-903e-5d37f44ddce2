#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析 000002 万科A 在 2024-03-12 和 2024-04-10 之间的 ZigZag 问题
"""

import pandas as pd
from datetime import datetime
from decimal import Decimal
from zigzag_analysis import ZigZagAnalyzer, StockData

def create_000002_simulation():
    """
    基于你观察到的问题，创建一个模拟 000002 的场景
    重现 "2024-03-12 和 2024-04-10 两个连续HIGH，中间无LOW" 的情况
    """
    
    # 模拟数据：基于你描述的情况
    data_points = [
        # 日期偏移, 开盘, 最高, 最低, 收盘
        # 3月初的上升趋势
        (0, 7.0, 7.1, 6.9, 7.05),   # 3/1
        (1, 7.05, 7.2, 7.0, 7.15),  # 3/2
        (2, 7.15, 7.3, 7.1, 7.25),  # 3/3
        (3, 7.25, 7.4, 7.2, 7.35),  # 3/4
        (4, 7.35, 7.5, 7.3, 7.45),  # 3/5
        (5, 7.45, 7.6, 7.4, 7.55),  # 3/6
        (6, 7.55, 7.7, 7.5, 7.65),  # 3/7
        (7, 7.65, 7.8, 7.6, 7.75),  # 3/8
        (8, 7.75, 7.9, 7.7, 7.85),  # 3/9
        (9, 7.85, 8.0, 7.8, 7.95),  # 3/10
        (10, 7.95, 8.1, 7.9, 8.05), # 3/11
        (11, 8.05, 8.2, 8.0, 8.15), # 3/12 - 第一个可能的HIGH点 (8.2)
        
        # 小幅回撤但不够深
        (12, 8.15, 8.1, 7.9, 7.95), # 3/13 - 回撤到7.9，但可能不够触发阈值
        (13, 7.95, 8.0, 7.8, 7.85), # 3/14 - 继续小幅下跌
        (14, 7.85, 7.9, 7.7, 7.75), # 3/15 - 最低到7.7
        
        # 然后开始反弹，但没有确认前面的HIGH为ZigZag点
        (15, 7.75, 7.8, 7.7, 7.78), # 3/16
        (16, 7.78, 7.85, 7.75, 7.82), # 3/17
        (17, 7.82, 7.9, 7.8, 7.88),  # 3/18
        (18, 7.88, 7.95, 7.85, 7.92), # 3/19
        (19, 7.92, 8.0, 7.9, 7.98),   # 3/20
        
        # 继续上升，可能创造新的更高的高点
        (20, 7.98, 8.05, 7.95, 8.02), # 3/21
        (21, 8.02, 8.1, 8.0, 8.08),   # 3/22
        (22, 8.08, 8.15, 8.05, 8.12), # 3/23
        (23, 8.12, 8.2, 8.1, 8.18),   # 3/24
        (24, 8.18, 8.25, 8.15, 8.22), # 3/25
        (25, 8.22, 8.3, 8.2, 8.28),   # 3/26
        (26, 8.28, 8.35, 8.25, 8.32), # 3/27
        (27, 8.32, 8.4, 8.3, 8.38),   # 3/28
        (28, 8.38, 8.45, 8.35, 8.42), # 3/29
        
        # 4月继续上升到新高
        (29, 8.42, 8.5, 8.4, 8.48),   # 4/1
        (30, 8.48, 8.55, 8.45, 8.52), # 4/2
        (31, 8.52, 8.6, 8.5, 8.58),   # 4/3
        (32, 8.58, 8.65, 8.55, 8.62), # 4/4
        (33, 8.62, 8.7, 8.6, 8.68),   # 4/5
        (34, 8.68, 8.75, 8.65, 8.72), # 4/6
        (35, 8.72, 8.8, 8.7, 8.78),   # 4/7
        (36, 8.78, 8.85, 8.75, 8.82), # 4/8
        (37, 8.82, 8.9, 8.8, 8.88),   # 4/9
        (38, 8.88, 8.98, 8.85, 8.95), # 4/10 - 第二个HIGH点 (8.98)
        
        # 然后明显回撤
        (39, 8.95, 8.9, 8.5, 8.55),   # 4/11 - 大幅回撤
        (40, 8.55, 8.6, 8.3, 8.35),   # 4/12 - 继续下跌
    ]
    
    base_date = datetime(2024, 3, 1)
    stock_data = []
    
    for day_offset, open_price, high_price, low_price, close_price in data_points:
        date = base_date + pd.Timedelta(days=day_offset)
        stock_data.append(StockData(
            symbol="000002",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        ))
    
    return stock_data

def analyze_zigzag_step_by_step():
    """逐步分析ZigZag算法的执行过程"""
    print("=" * 80)
    print("逐步分析 000002 ZigZag 算法执行过程")
    print("=" * 80)
    
    stock_data = create_000002_simulation()
    print(f"创建了 {len(stock_data)} 个数据点")
    
    # 创建分析器
    analyzer = ZigZagAnalyzer(
        base_threshold=0.05,  # 5% 阈值
        dynamic_threshold=True,  # 使用动态阈值（与批量分析一致）
        trend_lookback_points=4
    )
    
    # 手动执行算法，添加调试信息
    print("\n开始逐步执行 ZigZag 算法...")
    print("-" * 80)
    
    analyzed_data = analyzer.calculate_and_analyze(stock_data)
    
    # 提取ZigZag点
    zigzag_points = []
    for i, data in enumerate(analyzed_data):
        if data.is_zigzag_point:
            zigzag_points.append({
                'index': i,
                'date': data.date.strftime('%Y-%m-%d'),
                'type': data.zigzag_point_type,
                'price': float(data.high if data.zigzag_point_type == 'HIGH' else data.low),
                'close': float(data.close)
            })
    
    print(f"\n发现 {len(zigzag_points)} 个 ZigZag 点:")
    print("-" * 80)
    
    for i, point in enumerate(zigzag_points):
        print(f"{i+1:2d}. {point['date']} | {point['type']:4s} | "
              f"价格: {point['price']:6.2f} | 收盘: {point['close']:6.2f}")
    
    # 检查连续性
    print("\n" + "=" * 80)
    print("连续性检查:")
    print("=" * 80)
    
    consecutive_issues = []
    for i in range(len(zigzag_points) - 1):
        current = zigzag_points[i]
        next_point = zigzag_points[i + 1]
        
        if current['type'] == next_point['type']:
            consecutive_issues.append((current, next_point))
    
    if consecutive_issues:
        print(f"❌ 发现 {len(consecutive_issues)} 组连续的同类型点:")
        for i, (p1, p2) in enumerate(consecutive_issues):
            print(f"   {i+1}. {p1['date']} ({p1['type']}: {p1['price']:.2f}) -> "
                  f"{p2['date']} ({p2['type']}: {p2['price']:.2f})")
            
            # 分析为什么会出现连续点
            print(f"      分析：在 {p1['date']} 和 {p2['date']} 之间缺少 {'LOW' if p1['type'] == 'HIGH' else 'HIGH'} 点")
    else:
        print("✅ 没有连续的同类型点，ZigZag 序列正确")
    
    # 重点分析 3/12 和 4/10 附近
    print("\n" + "=" * 80)
    print("重点分析目标日期附近:")
    print("=" * 80)
    
    target_dates = ['2024-03-12', '2024-04-10']
    for target_date in target_dates:
        found_zigzag = False
        for point in zigzag_points:
            if point['date'] == target_date:
                print(f"✅ {target_date}: 发现 {point['type']} 点，价格 {point['price']:.2f}")
                found_zigzag = True
                break
        
        if not found_zigzag:
            # 查看该日期的原始数据
            for i, data in enumerate(analyzed_data):
                if data.date.strftime('%Y-%m-%d') == target_date:
                    print(f"📊 {target_date}: 非ZigZag点 - "
                          f"高:{float(data.high):.2f} 低:{float(data.low):.2f} 收:{float(data.close):.2f}")
                    break

if __name__ == "__main__":
    analyze_zigzag_step_by_step()
