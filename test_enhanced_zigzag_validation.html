<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="a929817104d240668c45ddcfbaffffbd" class="chart-container" style="width:100%; height:800px; "></div>
    <script>
        var chart_a929817104d240668c45ddcfbaffffbd = echarts.init(
            document.getElementById('a929817104d240668c45ddcfbaffffbd'), 'white', {renderer: 'canvas'});
            window.stockDataForTooltip = [{"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": true, "zigzag_point_type": "HIGH", "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}, {"trend_status": "等待更多数据", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1000000}]
            window.zigzagValidation = {"total_points": 1, "zigzag_points": [{"index": 40, "date": "2024-04-10", "type": "HIGH", "price": 10.4, "close": 10.35}], "consecutive_issues": [], "is_valid": true}
            document.addEventListener('DOMContentLoaded', function() {  });
        var option_a929817104d240668c45ddcfbaffffbd = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    7.0,
                    7.05,
                    6.9,
                    7.1
                ],
                [
                    7.05,
                    7.15,
                    7.0,
                    7.2
                ],
                [
                    7.15,
                    7.25,
                    7.1,
                    7.3
                ],
                [
                    7.25,
                    7.35,
                    7.2,
                    7.4
                ],
                [
                    7.35,
                    7.45,
                    7.3,
                    7.5
                ],
                [
                    7.45,
                    7.55,
                    7.4,
                    7.6
                ],
                [
                    7.55,
                    7.65,
                    7.5,
                    7.7
                ],
                [
                    7.65,
                    7.75,
                    7.6,
                    7.8
                ],
                [
                    7.75,
                    7.85,
                    7.7,
                    7.9
                ],
                [
                    7.85,
                    7.95,
                    7.8,
                    8.0
                ],
                [
                    7.95,
                    8.05,
                    7.9,
                    8.1
                ],
                [
                    8.05,
                    8.15,
                    8.0,
                    8.2
                ],
                [
                    8.15,
                    8.0,
                    7.95,
                    8.1
                ],
                [
                    8.0,
                    7.95,
                    7.9,
                    8.05
                ],
                [
                    7.95,
                    7.9,
                    7.85,
                    8.0
                ],
                [
                    7.9,
                    7.92,
                    7.85,
                    7.95
                ],
                [
                    7.92,
                    7.98,
                    7.9,
                    8.0
                ],
                [
                    7.98,
                    8.05,
                    7.95,
                    8.1
                ],
                [
                    8.05,
                    8.15,
                    8.0,
                    8.2
                ],
                [
                    8.15,
                    8.25,
                    8.1,
                    8.3
                ],
                [
                    8.25,
                    8.35,
                    8.2,
                    8.4
                ],
                [
                    8.35,
                    8.45,
                    8.3,
                    8.5
                ],
                [
                    8.45,
                    8.55,
                    8.4,
                    8.6
                ],
                [
                    8.55,
                    8.65,
                    8.5,
                    8.7
                ],
                [
                    8.65,
                    8.75,
                    8.6,
                    8.8
                ],
                [
                    8.75,
                    8.85,
                    8.7,
                    8.9
                ],
                [
                    8.85,
                    8.95,
                    8.8,
                    9.0
                ],
                [
                    8.95,
                    9.05,
                    8.9,
                    9.1
                ],
                [
                    9.05,
                    9.15,
                    9.0,
                    9.2
                ],
                [
                    9.15,
                    9.25,
                    9.1,
                    9.3
                ],
                [
                    9.25,
                    9.35,
                    9.2,
                    9.4
                ],
                [
                    9.35,
                    9.45,
                    9.3,
                    9.5
                ],
                [
                    9.45,
                    9.55,
                    9.4,
                    9.6
                ],
                [
                    9.55,
                    9.65,
                    9.5,
                    9.7
                ],
                [
                    9.65,
                    9.75,
                    9.6,
                    9.8
                ],
                [
                    9.75,
                    9.85,
                    9.7,
                    9.9
                ],
                [
                    9.85,
                    9.95,
                    9.8,
                    10.0
                ],
                [
                    9.95,
                    10.05,
                    9.9,
                    10.1
                ],
                [
                    10.05,
                    10.15,
                    10.0,
                    10.2
                ],
                [
                    10.15,
                    10.25,
                    10.1,
                    10.3
                ],
                [
                    10.25,
                    10.35,
                    10.2,
                    10.4
                ],
                [
                    10.35,
                    9.9,
                    9.8,
                    10.3
                ],
                [
                    9.9,
                    9.6,
                    9.5,
                    9.95
                ],
                [
                    9.6,
                    9.4,
                    9.3,
                    9.7
                ],
                [
                    9.4,
                    9.2,
                    9.1,
                    9.5
                ],
                [
                    9.2,
                    9.0,
                    8.9,
                    9.3
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#ec0000",
                "borderColor0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "ZigZag\u8d8b\u52bf\u7ebf",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "none",
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-03-01",
                    null
                ],
                [
                    "2024-03-02",
                    null
                ],
                [
                    "2024-03-03",
                    null
                ],
                [
                    "2024-03-04",
                    null
                ],
                [
                    "2024-03-05",
                    null
                ],
                [
                    "2024-03-06",
                    null
                ],
                [
                    "2024-03-07",
                    null
                ],
                [
                    "2024-03-08",
                    null
                ],
                [
                    "2024-03-09",
                    null
                ],
                [
                    "2024-03-10",
                    null
                ],
                [
                    "2024-03-11",
                    null
                ],
                [
                    "2024-03-12",
                    null
                ],
                [
                    "2024-03-13",
                    null
                ],
                [
                    "2024-03-14",
                    null
                ],
                [
                    "2024-03-15",
                    null
                ],
                [
                    "2024-03-16",
                    null
                ],
                [
                    "2024-03-17",
                    null
                ],
                [
                    "2024-03-18",
                    null
                ],
                [
                    "2024-03-19",
                    null
                ],
                [
                    "2024-03-20",
                    null
                ],
                [
                    "2024-03-21",
                    null
                ],
                [
                    "2024-03-22",
                    null
                ],
                [
                    "2024-03-23",
                    null
                ],
                [
                    "2024-03-24",
                    null
                ],
                [
                    "2024-03-25",
                    null
                ],
                [
                    "2024-03-26",
                    null
                ],
                [
                    "2024-03-27",
                    null
                ],
                [
                    "2024-03-28",
                    null
                ],
                [
                    "2024-03-29",
                    null
                ],
                [
                    "2024-03-30",
                    null
                ],
                [
                    "2024-03-31",
                    null
                ],
                [
                    "2024-04-01",
                    null
                ],
                [
                    "2024-04-02",
                    null
                ],
                [
                    "2024-04-03",
                    null
                ],
                [
                    "2024-04-04",
                    null
                ],
                [
                    "2024-04-05",
                    null
                ],
                [
                    "2024-04-06",
                    null
                ],
                [
                    "2024-04-07",
                    null
                ],
                [
                    "2024-04-08",
                    null
                ],
                [
                    "2024-04-09",
                    null
                ],
                [
                    "2024-04-10",
                    10.4
                ],
                [
                    "2024-04-11",
                    null
                ],
                [
                    "2024-04-12",
                    null
                ],
                [
                    "2024-04-13",
                    null
                ],
                [
                    "2024-04-14",
                    null
                ],
                [
                    "2024-04-15",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "dashed",
                "color": "#1890ff"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u5cf0\u70b9",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 14,
            "data": [
                [
                    "2024-03-01",
                    null
                ],
                [
                    "2024-03-02",
                    null
                ],
                [
                    "2024-03-03",
                    null
                ],
                [
                    "2024-03-04",
                    null
                ],
                [
                    "2024-03-05",
                    null
                ],
                [
                    "2024-03-06",
                    null
                ],
                [
                    "2024-03-07",
                    null
                ],
                [
                    "2024-03-08",
                    null
                ],
                [
                    "2024-03-09",
                    null
                ],
                [
                    "2024-03-10",
                    null
                ],
                [
                    "2024-03-11",
                    null
                ],
                [
                    "2024-03-12",
                    null
                ],
                [
                    "2024-03-13",
                    null
                ],
                [
                    "2024-03-14",
                    null
                ],
                [
                    "2024-03-15",
                    null
                ],
                [
                    "2024-03-16",
                    null
                ],
                [
                    "2024-03-17",
                    null
                ],
                [
                    "2024-03-18",
                    null
                ],
                [
                    "2024-03-19",
                    null
                ],
                [
                    "2024-03-20",
                    null
                ],
                [
                    "2024-03-21",
                    null
                ],
                [
                    "2024-03-22",
                    null
                ],
                [
                    "2024-03-23",
                    null
                ],
                [
                    "2024-03-24",
                    null
                ],
                [
                    "2024-03-25",
                    null
                ],
                [
                    "2024-03-26",
                    null
                ],
                [
                    "2024-03-27",
                    null
                ],
                [
                    "2024-03-28",
                    null
                ],
                [
                    "2024-03-29",
                    null
                ],
                [
                    "2024-03-30",
                    null
                ],
                [
                    "2024-03-31",
                    null
                ],
                [
                    "2024-04-01",
                    null
                ],
                [
                    "2024-04-02",
                    null
                ],
                [
                    "2024-04-03",
                    null
                ],
                [
                    "2024-04-04",
                    null
                ],
                [
                    "2024-04-05",
                    null
                ],
                [
                    "2024-04-06",
                    null
                ],
                [
                    "2024-04-07",
                    null
                ],
                [
                    "2024-04-08",
                    null
                ],
                [
                    "2024-04-09",
                    null
                ],
                [
                    "2024-04-10",
                    10.4
                ],
                [
                    "2024-04-11",
                    null
                ],
                [
                    "2024-04-12",
                    null
                ],
                [
                    "2024-04-13",
                    null
                ],
                [
                    "2024-04-14",
                    null
                ],
                [
                    "2024-04-15",
                    null
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#ff6b6b",
                "borderColor": "#ffffff",
                "borderWidth": 1
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "line",
            "name": "\u5cf0\u70b9\u8fde\u7ebf",
            "connectNulls": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "none",
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-03-01",
                    null
                ],
                [
                    "2024-03-02",
                    null
                ],
                [
                    "2024-03-03",
                    null
                ],
                [
                    "2024-03-04",
                    null
                ],
                [
                    "2024-03-05",
                    null
                ],
                [
                    "2024-03-06",
                    null
                ],
                [
                    "2024-03-07",
                    null
                ],
                [
                    "2024-03-08",
                    null
                ],
                [
                    "2024-03-09",
                    null
                ],
                [
                    "2024-03-10",
                    null
                ],
                [
                    "2024-03-11",
                    null
                ],
                [
                    "2024-03-12",
                    null
                ],
                [
                    "2024-03-13",
                    null
                ],
                [
                    "2024-03-14",
                    null
                ],
                [
                    "2024-03-15",
                    null
                ],
                [
                    "2024-03-16",
                    null
                ],
                [
                    "2024-03-17",
                    null
                ],
                [
                    "2024-03-18",
                    null
                ],
                [
                    "2024-03-19",
                    null
                ],
                [
                    "2024-03-20",
                    null
                ],
                [
                    "2024-03-21",
                    null
                ],
                [
                    "2024-03-22",
                    null
                ],
                [
                    "2024-03-23",
                    null
                ],
                [
                    "2024-03-24",
                    null
                ],
                [
                    "2024-03-25",
                    null
                ],
                [
                    "2024-03-26",
                    null
                ],
                [
                    "2024-03-27",
                    null
                ],
                [
                    "2024-03-28",
                    null
                ],
                [
                    "2024-03-29",
                    null
                ],
                [
                    "2024-03-30",
                    null
                ],
                [
                    "2024-03-31",
                    null
                ],
                [
                    "2024-04-01",
                    null
                ],
                [
                    "2024-04-02",
                    null
                ],
                [
                    "2024-04-03",
                    null
                ],
                [
                    "2024-04-04",
                    null
                ],
                [
                    "2024-04-05",
                    null
                ],
                [
                    "2024-04-06",
                    null
                ],
                [
                    "2024-04-07",
                    null
                ],
                [
                    "2024-04-08",
                    null
                ],
                [
                    "2024-04-09",
                    null
                ],
                [
                    "2024-04-10",
                    10.4
                ],
                [
                    "2024-04-11",
                    null
                ],
                [
                    "2024-04-12",
                    null
                ],
                [
                    "2024-04-13",
                    null
                ],
                [
                    "2024-04-14",
                    null
                ],
                [
                    "2024-04-15",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1.5,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff6b6b"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1000000,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                }
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            }
        },
        {
            "type": "line",
            "name": "VOL5",
            "connectNulls": true,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-03-01",
                    null
                ],
                [
                    "2024-03-02",
                    null
                ],
                [
                    "2024-03-03",
                    null
                ],
                [
                    "2024-03-04",
                    null
                ],
                [
                    "2024-03-05",
                    null
                ],
                [
                    "2024-03-06",
                    null
                ],
                [
                    "2024-03-07",
                    null
                ],
                [
                    "2024-03-08",
                    null
                ],
                [
                    "2024-03-09",
                    null
                ],
                [
                    "2024-03-10",
                    null
                ],
                [
                    "2024-03-11",
                    null
                ],
                [
                    "2024-03-12",
                    null
                ],
                [
                    "2024-03-13",
                    null
                ],
                [
                    "2024-03-14",
                    null
                ],
                [
                    "2024-03-15",
                    null
                ],
                [
                    "2024-03-16",
                    null
                ],
                [
                    "2024-03-17",
                    null
                ],
                [
                    "2024-03-18",
                    null
                ],
                [
                    "2024-03-19",
                    null
                ],
                [
                    "2024-03-20",
                    null
                ],
                [
                    "2024-03-21",
                    null
                ],
                [
                    "2024-03-22",
                    null
                ],
                [
                    "2024-03-23",
                    null
                ],
                [
                    "2024-03-24",
                    null
                ],
                [
                    "2024-03-25",
                    null
                ],
                [
                    "2024-03-26",
                    null
                ],
                [
                    "2024-03-27",
                    null
                ],
                [
                    "2024-03-28",
                    null
                ],
                [
                    "2024-03-29",
                    null
                ],
                [
                    "2024-03-30",
                    null
                ],
                [
                    "2024-03-31",
                    null
                ],
                [
                    "2024-04-01",
                    null
                ],
                [
                    "2024-04-02",
                    null
                ],
                [
                    "2024-04-03",
                    null
                ],
                [
                    "2024-04-04",
                    null
                ],
                [
                    "2024-04-05",
                    null
                ],
                [
                    "2024-04-06",
                    null
                ],
                [
                    "2024-04-07",
                    null
                ],
                [
                    "2024-04-08",
                    null
                ],
                [
                    "2024-04-09",
                    null
                ],
                [
                    "2024-04-10",
                    null
                ],
                [
                    "2024-04-11",
                    null
                ],
                [
                    "2024-04-12",
                    null
                ],
                [
                    "2024-04-13",
                    null
                ],
                [
                    "2024-04-14",
                    null
                ],
                [
                    "2024-04-15",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1.5,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff9900"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "VOL20",
            "connectNulls": true,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-03-01",
                    null
                ],
                [
                    "2024-03-02",
                    null
                ],
                [
                    "2024-03-03",
                    null
                ],
                [
                    "2024-03-04",
                    null
                ],
                [
                    "2024-03-05",
                    null
                ],
                [
                    "2024-03-06",
                    null
                ],
                [
                    "2024-03-07",
                    null
                ],
                [
                    "2024-03-08",
                    null
                ],
                [
                    "2024-03-09",
                    null
                ],
                [
                    "2024-03-10",
                    null
                ],
                [
                    "2024-03-11",
                    null
                ],
                [
                    "2024-03-12",
                    null
                ],
                [
                    "2024-03-13",
                    null
                ],
                [
                    "2024-03-14",
                    null
                ],
                [
                    "2024-03-15",
                    null
                ],
                [
                    "2024-03-16",
                    null
                ],
                [
                    "2024-03-17",
                    null
                ],
                [
                    "2024-03-18",
                    null
                ],
                [
                    "2024-03-19",
                    null
                ],
                [
                    "2024-03-20",
                    null
                ],
                [
                    "2024-03-21",
                    null
                ],
                [
                    "2024-03-22",
                    null
                ],
                [
                    "2024-03-23",
                    null
                ],
                [
                    "2024-03-24",
                    null
                ],
                [
                    "2024-03-25",
                    null
                ],
                [
                    "2024-03-26",
                    null
                ],
                [
                    "2024-03-27",
                    null
                ],
                [
                    "2024-03-28",
                    null
                ],
                [
                    "2024-03-29",
                    null
                ],
                [
                    "2024-03-30",
                    null
                ],
                [
                    "2024-03-31",
                    null
                ],
                [
                    "2024-04-01",
                    null
                ],
                [
                    "2024-04-02",
                    null
                ],
                [
                    "2024-04-03",
                    null
                ],
                [
                    "2024-04-04",
                    null
                ],
                [
                    "2024-04-05",
                    null
                ],
                [
                    "2024-04-06",
                    null
                ],
                [
                    "2024-04-07",
                    null
                ],
                [
                    "2024-04-08",
                    null
                ],
                [
                    "2024-04-09",
                    null
                ],
                [
                    "2024-04-10",
                    null
                ],
                [
                    "2024-04-11",
                    null
                ],
                [
                    "2024-04-12",
                    null
                ],
                [
                    "2024-04-13",
                    null
                ],
                [
                    "2024-04-14",
                    null
                ],
                [
                    "2024-04-15",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1.5,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#3b8cff"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "ZigZag\u8d8b\u52bf\u7ebf",
                "\u5cf0\u70b9",
                "\u5cf0\u70b9\u8fde\u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf",
                "VOL5",
                "VOL20"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter":                         function (params) {                            var k = null;                            for (var i = 0; i < params.length; i++) {                                if (params[i].seriesType === 'candlestick') { k = params[i]; break; }                            }                            if (!k && params.length) { k = params[0]; }                            if (!k) { return ''; }                            var idx = k.dataIndex || 0;                            var sdArr = window.stockDataForTooltip || [];                            var sd = sdArr[idx] || {};                            var v = Array.isArray(k.value) ? k.value : [];                            function num(n){ return (n == null || isNaN(n)) ? '-' : Number(n).toFixed(2); }                            var open = num(v[1]);                            var close = num(v[2]);                            var low = num(v[3]);                            var high = num(v[4]);                            var parts = [];                            parts.push('\u65e5\u671f: ' + (k.axisValue || ''));                            parts.push('\u5f00\u76d8: ' + open);                            parts.push('\u6536\u76d8: ' + close);                            parts.push('\u6700\u4f4e: ' + low);                            parts.push('\u6700\u9ad8: ' + high);                            parts.push('\u6210\u4ea4\u91cf: ' + (sd.volume != null ? Number(sd.volume).toLocaleString() : '-'));                            if (sd.is_zigzag_point) { parts.push('\u5cf0\u8c37\u70b9: ' + (sd.zigzag_point_type || '')); }                            parts.push('\u8d8b\u52bf\u72b6\u6001: ' + (sd.trend_status || '\u6682\u65e0'));                            return parts.join('<br/>');                        }                    ,
        "textStyle": {
            "color": "#000",
            "fontSize": 12
        },
        "backgroundColor": "rgba(245, 245, 245, 0.95)",
        "borderColor": "#ccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 20,
            "boundaryGap": false,
            "min": "dataMin",
            "max": "dataMax",
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-03-01",
                "2024-03-02",
                "2024-03-03",
                "2024-03-04",
                "2024-03-05",
                "2024-03-06",
                "2024-03-07",
                "2024-03-08",
                "2024-03-09",
                "2024-03-10",
                "2024-03-11",
                "2024-03-12",
                "2024-03-13",
                "2024-03-14",
                "2024-03-15",
                "2024-03-16",
                "2024-03-17",
                "2024-03-18",
                "2024-03-19",
                "2024-03-20",
                "2024-03-21",
                "2024-03-22",
                "2024-03-23",
                "2024-03-24",
                "2024-03-25",
                "2024-03-26",
                "2024-03-27",
                "2024-03-28",
                "2024-03-29",
                "2024-03-30",
                "2024-03-31",
                "2024-04-01",
                "2024-04-02",
                "2024-04-03",
                "2024-04-04",
                "2024-04-05",
                "2024-04-06",
                "2024-04-07",
                "2024-04-08",
                "2024-04-09",
                "2024-04-10",
                "2024-04-11",
                "2024-04-12",
                "2024-04-13",
                "2024-04-14",
                "2024-04-15"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-03-01",
                "2024-03-02",
                "2024-03-03",
                "2024-03-04",
                "2024-03-05",
                "2024-03-06",
                "2024-03-07",
                "2024-03-08",
                "2024-03-09",
                "2024-03-10",
                "2024-03-11",
                "2024-03-12",
                "2024-03-13",
                "2024-03-14",
                "2024-03-15",
                "2024-03-16",
                "2024-03-17",
                "2024-03-18",
                "2024-03-19",
                "2024-03-20",
                "2024-03-21",
                "2024-03-22",
                "2024-03-23",
                "2024-03-24",
                "2024-03-25",
                "2024-03-26",
                "2024-03-27",
                "2024-03-28",
                "2024-03-29",
                "2024-03-30",
                "2024-03-31",
                "2024-04-01",
                "2024-04-02",
                "2024-04-03",
                "2024-04-04",
                "2024-04-05",
                "2024-04-06",
                "2024-04-07",
                "2024-04-08",
                "2024-04-09",
                "2024-04-10",
                "2024-04-11",
                "2024-04-12",
                "2024-04-13",
                "2024-04-14",
                "2024-04-15"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 3,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u80a1\u7968K\u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "left": "0",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": false,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "top": "90%",
            "filterMode": "filter"
        },
        [
            {
                "show": false,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "top": "90%",
                "filterMode": "filter"
            }
        ]
    ],
    "axisPointer": {
        "show": true,
        "type": "line",
        "link": [
            {
                "xAxisIndex": "all"
            }
        ],
        "label": {
            "show": true,
            "margin": 8,
            "backgroundColor": "#777",
            "valueAnimation": false
        },
        "triggerTooltip": true,
        "triggerOn": "mousemove|click"
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "right": "8%",
            "height": "60%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "16%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_a929817104d240668c45ddcfbaffffbd.setOption(option_a929817104d240668c45ddcfbaffffbd);
            window.addEventListener('resize', function(){
                chart_a929817104d240668c45ddcfbaffffbd.resize();
            })
    </script>
</body>
</html>
