#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 000002 万科A 在 2024-03-01 到 2024-04-20 期间的 ZigZag 点
验证是否存在连续两个 HIGH 而无 LOW 的情况
"""

from zigzag_analysis import ZigZagAnalyzer, get_stock_data, dataframe_to_stock_data

def debug_zigzag_000002():
    """调试 000002 的 ZigZag 点序列"""
    print("=" * 60)
    print("调试 000002 万科A ZigZag 点序列")
    print("=" * 60)
    
    # 获取数据
    stock_code = "000002"
    start_date = "20240301"
    end_date = "20240420"
    
    print(f"股票代码: {stock_code}")
    print(f"分析期间: {start_date} - {end_date}")
    print("-" * 60)
    
    try:
        # 获取股票数据
        df = get_stock_data(stock_code, start_date, end_date)
        stock_data_list = dataframe_to_stock_data(df, stock_code)
        
        print(f"获取到 {len(stock_data_list)} 个交易日的数据")
        
        # 创建分析器（使用与批量分析相同的参数）
        analyzer = ZigZagAnalyzer(
            base_threshold=0.05,
            dynamic_threshold=True,
            trend_lookback_points=4
        )
        
        # 执行分析
        analyzed_data = analyzer.calculate_and_analyze(stock_data_list)
        
        # 提取所有 ZigZag 点
        zigzag_points = []
        for i, data in enumerate(analyzed_data):
            if data.is_zigzag_point:
                zigzag_points.append({
                    'index': i,
                    'date': data.date.strftime('%Y-%m-%d'),
                    'type': data.zigzag_point_type,
                    'price': float(data.high if data.zigzag_point_type == 'HIGH' else data.low),
                    'close': float(data.close)
                })
        
        print(f"\n发现 {len(zigzag_points)} 个 ZigZag 点:")
        print("-" * 60)
        
        for i, point in enumerate(zigzag_points):
            print(f"{i+1:2d}. {point['date']} | {point['type']:4s} | "
                  f"价格: {point['price']:6.2f} | 收盘: {point['close']:6.2f}")
        
        # 检查是否有连续的 HIGH 或 LOW
        print("\n" + "=" * 60)
        print("连续性检查:")
        print("=" * 60)
        
        consecutive_highs = []
        consecutive_lows = []
        
        for i in range(len(zigzag_points) - 1):
            current = zigzag_points[i]
            next_point = zigzag_points[i + 1]
            
            if current['type'] == next_point['type']:
                if current['type'] == 'HIGH':
                    consecutive_highs.append((current, next_point))
                else:
                    consecutive_lows.append((current, next_point))
        
        if consecutive_highs:
            print(f"❌ 发现 {len(consecutive_highs)} 组连续的 HIGH 点:")
            for i, (p1, p2) in enumerate(consecutive_highs):
                print(f"   {i+1}. {p1['date']} ({p1['price']:.2f}) -> {p2['date']} ({p2['price']:.2f})")
        else:
            print("✅ 没有连续的 HIGH 点")
        
        if consecutive_lows:
            print(f"❌ 发现 {len(consecutive_lows)} 组连续的 LOW 点:")
            for i, (p1, p2) in enumerate(consecutive_lows):
                print(f"   {i+1}. {p1['date']} ({p1['price']:.2f}) -> {p2['date']} ({p2['price']:.2f})")
        else:
            print("✅ 没有连续的 LOW 点")
        
        # 特别关注 3/12 和 4/10 附近
        print("\n" + "=" * 60)
        print("重点关注 2024-03-12 和 2024-04-10 附近:")
        print("=" * 60)
        
        target_dates = ['2024-03-12', '2024-04-10']
        for target_date in target_dates:
            found = False
            for point in zigzag_points:
                if point['date'] == target_date:
                    print(f"✅ {target_date}: {point['type']} 点，价格 {point['price']:.2f}")
                    found = True
                    break
            if not found:
                print(f"❌ {target_date}: 未找到 ZigZag 点")
        
        # 显示 3/12 到 4/10 之间的所有 ZigZag 点
        print(f"\n2024-03-12 到 2024-04-10 之间的所有 ZigZag 点:")
        between_points = [p for p in zigzag_points 
                         if '2024-03-12' <= p['date'] <= '2024-04-10']
        
        if between_points:
            for point in between_points:
                print(f"   {point['date']} | {point['type']} | {point['price']:.2f}")
        else:
            print("   无 ZigZag 点")
            
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_zigzag_000002()
