import akshare as ak
import pandas as pd
from datetime import datetime
from dataclasses import dataclass
import json
import os
from decimal import Decimal, getcontext
from typing import List, Tuple, Dict, Any, Optional

# 设置Decimal精度
getcontext().prec = 28

@dataclass
class StockData:
    """股票数据模型"""
    symbol: str
    date: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    
    # 技术指标字段
    vol5: Optional[Decimal] = None
    vol20: Optional[Decimal] = None

    # 新增的 ZigZag 和趋势字段
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None  # 'HIGH' or 'LOW'
    trend_status: Optional[str] = None

@dataclass
class ZigZagPoint:
    """ZigZag转折点数据类"""
    index: int
    price: Decimal
    point_type: str  # 'HIGH' or 'LOW'
    date: str
    threshold_used: Decimal  # 记录使用的阈值
    

def get_stock_data(stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    从 AKShare 获取股票历史数据
    :param stock_code: 股票代码 (如 "600000")
    :param start_date: 开始日期 (格式 "YYYYMMDD")
    :param end_date: 结束日期 (格式 "YYYYMMDD")
    :return: 包含 OHLCV 数据的 DataFrame
    """
    try:
        # 获取后复权数据（包含分红送股）
        df = ak.stock_zh_a_hist(
            symbol=stock_code, 
            period="daily", 
            start_date=start_date, 
            end_date=end_date, 
            # adjust="qfq"
        )
        
        # 规范列名
        df = df.rename(columns={
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume'
        })
        
        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # 计算成交量均线（5日与20日移动平均）
        df['vol5'] = df['volume'].rolling(window=5, min_periods=1).mean()
        df['vol20'] = df['volume'].rolling(window=20, min_periods=1).mean()

        return df[['date', 'open', 'high', 'low', 'close', 'volume', 'vol5', 'vol20']]
    
    except Exception as e:
        raise RuntimeError(f"获取 {stock_code} 数据失败: {str(e)}")

def dataframe_to_stock_data(df: pd.DataFrame, symbol: str) -> List[StockData]:
    """将DataFrame转换为StockData列表"""
    stock_data_list = []
    
    for _, row in df.iterrows():
        stock_data = StockData(
            symbol=symbol,
            date=row['date'].to_pydatetime(),
            open=Decimal(str(row['open'])),
            high=Decimal(str(row['high'])),
            low=Decimal(str(row['low'])),
            close=Decimal(str(row['close'])),
            volume=int(row['volume']),
            vol5=Decimal(str(row.get('vol5'))) if 'vol5' in row else None,
            vol20=Decimal(str(row.get('vol20'))) if 'vol20' in row else None,
        )
        stock_data_list.append(stock_data)
    
    return stock_data_list

class ZigZagAnalyzer:
    """
    ZigZag分析器，包含动态阈值功能
    
    ZigZag指标用于识别价格趋势的转折点，过滤掉小幅波动的噪音。
    """
    
    def __init__(self, 
                 base_threshold: float = 0.05, 
                 dynamic_threshold: bool = True, 
                 atr_period: int = 14,
                 atr_multiplier: float = 2.0,
                 confirm_period: int = 5, 
                 trend_lookback_points: int = 4):
        """
        初始化ZigZag分析器
        
        Args:
            base_threshold: 基础阈值（百分比）
            dynamic_threshold: 是否使用动态阈值
            atr_period: ATR计算周期
            atr_multiplier: ATR倍数，用于计算动态阈值
        """
        self.base_threshold = Decimal(str(base_threshold))
        self.dynamic_threshold = dynamic_threshold
        self.atr_period = atr_period
        self.atr_multiplier = Decimal(str(atr_multiplier))
        self.confirm_period = confirm_period
        self.trend_lookback_points = trend_lookback_points
        
        # 缓存ATR值避免重复计算
        self._atr_cache: List[Decimal] = []
        self._breakout_cache: Optional[Dict[str, Any]] = None # 新增的突破缓存
    
    def _calculate_atr(self, stock_data: List[StockData]) -> List[Decimal]:
        """
        计算平均真实波幅(ATR)
        使用指数移动平均的方法提高计算效率
        """
        if len(stock_data) < 2:
            return []
        
        true_ranges = []
        for i in range(1, len(stock_data)):
            current = stock_data[i]
            previous = stock_data[i-1]
            
            # 计算真实波幅
            tr1 = current.high - current.low
            tr2 = abs(current.high - previous.close)
            tr3 = abs(current.low - previous.close)
            
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        # 使用指数移动平均计算ATR，提高效率
        atr_values = []
        alpha = Decimal('2') / (Decimal(str(self.atr_period)) + Decimal('1'))
        
        for i, tr in enumerate(true_ranges):
            if i == 0:
                # 第一个ATR值就是第一个TR值
                atr = tr
            else:
                # EMA公式: ATR = alpha * TR + (1 - alpha) * 前一个ATR
                atr = alpha * tr + (Decimal('1') - alpha) * atr_values[-1]
            
            atr_values.append(atr)
        
        # 在数组开头添加一个0，使索引与stock_data对齐
        return [Decimal('0')] + atr_values
    
    def _get_dynamic_threshold(self, 
                              index: int, 
                              stock_data: List[StockData], 
                              atr_values: List[Decimal]) -> Decimal:
        """计算动态阈值"""
        if not self.dynamic_threshold or index >= len(atr_values) or atr_values[index] == 0:
            return self.base_threshold
        
        # 使用更稳定的基准价格
        lookback_period = min(20, index)
        if lookback_period == 0:
            base_price = stock_data[index].close
        else:
            # 使用过去N天的平均价格作为基准
            start_idx = max(0, index - lookback_period)
            prices = [stock_data[i].close for i in range(start_idx, index + 1)]
            base_price = sum(prices) / len(prices)
        
        if base_price == 0:
            return self.base_threshold
        
        # 计算动态阈值
        dynamic_threshold = (atr_values[index] * self.atr_multiplier) / base_price
        
        # 限制阈值范围
        min_threshold = self.base_threshold * Decimal('0.3')
        max_threshold = self.base_threshold * Decimal('3.0')
        
        return max(min_threshold, min(dynamic_threshold, max_threshold))
    
    def _calculate_slope(self, point1: ZigZagPoint, point2: ZigZagPoint) -> Decimal:
        if point1.index == point2.index:
            return Decimal('0')
        return (point2.price - point1.price) / Decimal(point2.index - point1.index)
        
    def _get_line_equation(self, p1: ZigZagPoint, p2: ZigZagPoint) -> Dict[str, Decimal]:
        """计算两点确定的直线方程的斜率和截距"""
        slope = self._calculate_slope(p1, p2)
        # y = mx + b => b = y - mx
        intercept = p1.price - slope * Decimal(p1.index)
        return {'slope': slope, 'intercept': intercept}

    def _project_price(self, line_eq: Dict[str, Decimal], target_index: int) -> Decimal:
        """根据直线方程预测某一天的价格"""
        return line_eq['slope'] * Decimal(target_index) + line_eq['intercept']

    def _analyze_dynamic_trend_status(self, zigzag_points: List[ZigZagPoint], current_data: StockData, stock_data: List[StockData]) -> str:
        """
        优化后的动态趋势分析方法，包含回退机制和成交量确认。
        """
        if len(zigzag_points) < 2:
            return '等待更多数据'
        
        # --- 1. 获取用于构建通道线的点 ---
        if len(zigzag_points) < 4:
            return "等待更多数据"

        recent_points = sorted(zigzag_points, key=lambda p: p.index, reverse=True)[:4]
        recent_points.sort(key=lambda p: p.index)

        peaks = sorted([p for p in recent_points if p.point_type == 'HIGH'], key=lambda p: p.index)
        valleys = sorted([p for p in recent_points if p.point_type == 'LOW'], key=lambda p: p.index)
        
        if len(peaks) < 2 or len(valleys) < 2:
            return "无明确规律的震荡"

        # --- 2. 核心逻辑：判断是否存在缓存的突破点 ---
        temp_peaks = peaks[:]
        temp_valleys = valleys[:]
        
        if self._breakout_cache:
            last_breakout = self._breakout_cache
            breakout_point = ZigZagPoint(
                index=last_breakout['index'],
                price=last_breakout['price'],
                point_type=last_breakout['type'],
                date=last_breakout['date'],
                threshold_used=Decimal('0')
            )
            
            if last_breakout['type'] == 'UPPER_BREAKOUT' and temp_valleys:
                temp_valleys[-1] = breakout_point
            elif last_breakout['type'] == 'LOWER_BREAKOUT' and temp_peaks:
                temp_peaks[-1] = breakout_point

        # --- 3. 构建通道线并进行分析 ---
        peak_line_eq = self._get_line_equation(temp_peaks[-2], temp_peaks[-1])
        valley_line_eq = self._get_line_equation(temp_valleys[-2], temp_valleys[-1])

        current_index = stock_data.index(current_data)
        projected_high_price = self._project_price(peak_line_eq, current_index)
        projected_low_price = self._project_price(valley_line_eq, current_index)
        
        peak_slope = peak_line_eq['slope']
        valley_slope = valley_line_eq['slope']
        
        slope_threshold = Decimal('0.0001')

        is_rising_channel = peak_slope > slope_threshold and valley_slope > slope_threshold
        is_falling_channel = peak_slope < -slope_threshold and valley_slope < -slope_threshold
        is_symmetrical_triangle = peak_slope < -slope_threshold and valley_slope > slope_threshold
        is_broadening_formation = peak_slope > slope_threshold and valley_slope < -slope_threshold
        is_sideways = abs(peak_slope) <= slope_threshold and abs(valley_slope) <= slope_threshold
        
        status = "无明确规律的震荡" # 默认状态
        
        if is_rising_channel:
            if current_data.high > projected_high_price:
                status = "上升趋势（突破上轨）"
            elif current_data.low < projected_low_price:
                status = "上升趋势（跌破下轨）"
            else:
                status = "上升通道"
        elif is_falling_channel:
            if current_data.low < projected_low_price:
                status = "下降趋势（突破下轨）"
            elif current_data.high > projected_high_price:
                status = "下降趋势（突破上轨）"
            else:
                status = "下降通道"
        elif is_symmetrical_triangle:
            if current_data.high > projected_high_price or current_data.low < projected_low_price:
                status = "收敛三角（突破）"
            else:
                status = "收敛三角形"
        elif is_broadening_formation:
            # 增加成交量确认逻辑
            status = "扩散三角形"
        elif is_sideways:
            if current_data.high > projected_high_price:
                status = "盘整（向上突破）"
            elif current_data.low < projected_low_price:
                status = "盘整（向下突破）"
            else:
                status = "窄幅盘整"
        else:
            status = "无明确规律的震荡"

        return status
    

    def calculate_and_analyze(self, stock_data: List[StockData]) -> List[StockData]:
        """
        整合所有功能，逐日计算 ZigZag 并判断趋势状态。
        """
        if len(stock_data) < self.trend_lookback_points + 2:
            return stock_data
        
        zigzag_points: List[ZigZagPoint] = []
        current_trend: Optional[str] = None
        pivot_idx = 0
        
        atr_values = self._calculate_atr(stock_data) if self.dynamic_threshold else []

        for i in range(1, len(stock_data)):
            current_day = stock_data[i]
            
            # --- 1. 寻找和确认 ZigZag 转折点 ---
            if current_trend == 'UP':
                if current_day.high > stock_data[pivot_idx].high:
                    pivot_idx = i
                decline = (stock_data[pivot_idx].high - current_day.low) / stock_data[pivot_idx].high
                threshold = self._get_dynamic_threshold(i, stock_data, atr_values)
                if decline >= threshold:
                    zigzag_points.append(ZigZagPoint(
                        index=pivot_idx, price=stock_data[pivot_idx].high, point_type='HIGH',
                        date=stock_data[pivot_idx].date.strftime('%Y-%m-%d'), threshold_used=threshold
                    ))
                    stock_data[pivot_idx].is_zigzag_point = True
                    stock_data[pivot_idx].zigzag_point_type = 'HIGH'
                    current_trend = 'DOWN'
                    pivot_idx = i
            elif current_trend == 'DOWN':
                if current_day.low < stock_data[pivot_idx].low:
                    pivot_idx = i
                rise = (current_day.high - stock_data[pivot_idx].low) / stock_data[pivot_idx].low
                threshold = self._get_dynamic_threshold(i, stock_data, atr_values)
                if rise >= threshold:
                    zigzag_points.append(ZigZagPoint(
                        index=pivot_idx, price=stock_data[pivot_idx].low, point_type='LOW',
                        date=stock_data[pivot_idx].date.strftime('%Y-%m-%d'), threshold_used=threshold
                    ))
                    stock_data[pivot_idx].is_zigzag_point = True
                    stock_data[pivot_idx].zigzag_point_type = 'LOW'
                    current_trend = 'UP'
                    pivot_idx = i
            else: # 初始状态
                threshold = self._get_dynamic_threshold(i, stock_data, atr_values)
                high_change = (current_day.high - stock_data[pivot_idx].high) / stock_data[pivot_idx].high
                low_change = (stock_data[pivot_idx].low - current_day.low) / stock_data[pivot_idx].low
                if high_change >= threshold:
                    current_trend = 'UP'
                    pivot_idx = i
                elif low_change >= threshold:
                    current_trend = 'DOWN'
                    pivot_idx = i

            # --- 2. 逐日更新趋势状态 ---
            prev_status = stock_data[i-1].trend_status if i > 0 else None
            if prev_status in ["上升趋势（突破上轨）", "下降趋势（突破上轨）", "收敛三角（突破）", "盘整（向上突破）"]:
                self._breakout_cache = {
                    'index': i - 1,
                    'price': stock_data[i-1].high,
                    'type': 'UPPER_BREAKOUT',
                    'date': stock_data[i-1].date.strftime('%Y-%m-%d')
                }
            elif prev_status in ["下降趋势（突破下轨）", "上升趋势（跌破下轨）", "盘整（向下突破）"]:
                self._breakout_cache = {
                    'index': i - 1,
                    'price': stock_data[i-1].low,
                    'type': 'LOWER_BREAKOUT',
                    'date': stock_data[i-1].date.strftime('%Y-%m-%d')
                }
            else:
                self._breakout_cache = None

            # 然后，用更新后的缓存状态来分析当天的趋势
            stock_data[i].trend_status = self._analyze_dynamic_trend_status(zigzag_points, current_day, stock_data)
            
            # 如果当前日形成新的zigzag点，则清空缓存，因为新的趋势线将从这个新点开始
            if stock_data[i].is_zigzag_point:
                self._breakout_cache = None
            
        return stock_data
    
    def get_zigzag_statistics(self, zigzag_points: List[ZigZagPoint]) -> Dict[str, Any]:
        """获取ZigZag统计信息"""
        if len(zigzag_points) < 2:
            return {}
        
        # 计算波动统计
        price_changes = []
        time_intervals = []
        
        for i in range(1, len(zigzag_points)):
            prev_point = zigzag_points[i-1]
            curr_point = zigzag_points[i]
            
            # 价格变化百分比
            price_change = abs(curr_point.price - prev_point.price) / prev_point.price
            price_changes.append(float(price_change))
            
            # 时间间隔
            time_intervals.append(curr_point.index - prev_point.index)
        
        return {
            'total_points': len(zigzag_points),
            'high_points': len([p for p in zigzag_points if p.point_type == 'HIGH']),
            'low_points': len([p for p in zigzag_points if p.point_type == 'LOW']),
            'avg_price_change': sum(price_changes) / len(price_changes) if price_changes else 0,
            'max_price_change': max(price_changes) if price_changes else 0,
            'min_price_change': min(price_changes) if price_changes else 0,
            'avg_time_interval': sum(time_intervals) / len(time_intervals) if time_intervals else 0,
            'max_time_interval': max(time_intervals) if time_intervals else 0,
            'min_time_interval': min(time_intervals) if time_intervals else 0,
        }
    
    def export_to_json(self, zigzag_points: List[ZigZagPoint], filename: str) -> None:
        """导出ZigZag点到JSON文件"""
        data = {
            'zigzag_points': [
                {
                    'index': point.index,
                    'price': str(point.price),
                    'type': point.point_type,
                    'date': point.date,
                    'threshold_used': str(point.threshold_used)
                }
                for point in zigzag_points
            ],
            'statistics': self.get_zigzag_statistics(zigzag_points),
            'parameters': {
                'base_threshold': str(self.base_threshold),
                'dynamic_threshold': self.dynamic_threshold,
                'atr_period': self.atr_period,
                'atr_multiplier': str(self.atr_multiplier)
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
if __name__ == '__main__':
    # 你的数据获取函数
    df = get_stock_data("600000", "20230101", "20240101")
    stock_data_list = dataframe_to_stock_data(df, "600000")

    # 初始化新的分析器
    # 注意：这里的参数是为你之前讨论的zigzag和趋势分析服务的
    # confirm_period 在这个新方法中不再直接使用，而是由 threshold_used 机制取代，
    # 实现了你最初的zigzag确认逻辑。
    analyzer = ZigZagAnalyzer(
        base_threshold=0.05,
        dynamic_threshold=True,
        trend_lookback_points=4
    )

    # 调用核心方法，一次性完成所有计算和分析
    analyzed_stock_data = analyzer.calculate_and_analyze(stock_data_list)

    # 打印最终结果
    for data in analyzed_stock_data:
        if data.is_zigzag_point:
            print(f"日期: {data.date.strftime('%Y-%m-%d')}, "
                  f"价格: {data.close:.2f}, "
                  f"类型: {data.zigzag_point_type}, "
                  f"趋势状态: {data.trend_status}")
        else:
             print(f"日期: {data.date.strftime('%Y-%m-%d')}, "
                  f"价格: {data.close:.2f}, "
                  f"趋势状态: {data.trend_status}")