#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成 000002 万科A 的可视化，包含 ZigZag 验证功能
"""

from stock_visualization import visualize_stock_analysis

def regenerate_000002_analysis():
    """重新生成 000002 的分析，包含验证功能"""
    print("=" * 80)
    print("重新生成 000002 万科A 分析（包含 ZigZag 验证）")
    print("=" * 80)
    
    try:
        # 使用与原批量分析相同的参数
        html_file = visualize_stock_analysis(
            stock_code="000002",
            start_date="20240101",  # 扩大时间范围以包含更多数据
            end_date="20240430",
            base_threshold=0.05,
            output_file="000002_万科A_增强验证分析.html"
        )
        
        print(f"\n✅ 增强版分析完成！")
        print(f"📊 新的图表文件: {html_file}")
        print("\n🔍 新增功能:")
        print("   - 如果检测到连续的同类型 ZigZag 点，右上角会显示黄色警告面板")
        print("   - 警告面板会显示:")
        print("     • 具体的连续点日期和价格")
        print("     • 缺失的点类型（HIGH 或 LOW）")
        print("     • 建议的缺失点位置和价格")
        print("   - 可以点击面板右上角的 × 关闭警告")
        print("\n📋 使用说明:")
        print("   1. 打开生成的 HTML 文件")
        print("   2. 如果存在 ZigZag 序列问题，会自动显示警告面板")
        print("   3. 对比警告面板中的信息与图表上的标记")
        print("   4. 验证是否确实存在你观察到的连续 HIGH 点问题")
        
        return html_file
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        print("\n💡 如果是网络问题，你可以:")
        print("   1. 检查网络连接")
        print("   2. 或者等待网络恢复后重试")
        print("   3. 或者使用现有的 HTML 文件进行对比分析")
        return None

if __name__ == "__main__":
    regenerate_000002_analysis()
