#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的 ZigZag 算法
使用模拟数据验证是否还会出现连续 HIGH 无 LOW 的问题
"""

import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from zigzag_analysis import ZigZagAnalyzer, StockData

def create_test_data():
    """创建模拟的测试数据，重现连续HIGH无LOW的场景"""
    base_date = datetime(2024, 3, 1)
    test_data = []
    
    # 模拟数据：创造一个会导致连续HIGH的场景
    prices = [
        # 日期, 开盘, 最高, 最低, 收盘
        (0, 7.0, 7.2, 6.9, 7.1),   # 3/1
        (1, 7.1, 7.3, 7.0, 7.2),   # 3/2
        (2, 7.2, 7.4, 7.1, 7.3),   # 3/3
        (3, 7.3, 7.5, 7.2, 7.4),   # 3/4 - 可能的第一个HIGH
        (4, 7.4, 7.6, 7.3, 7.5),   # 3/5 - 更高的HIGH
        (5, 7.5, 7.8, 7.4, 7.7),   # 3/6 - 继续创新高
        (6, 7.7, 7.9, 7.6, 7.8),   # 3/7 - 最高点
        (7, 7.8, 7.9, 7.4, 7.5),   # 3/8 - 开始回撤，但最低7.4
        (8, 7.5, 7.6, 7.2, 7.3),   # 3/9 - 继续回撤到7.2
        (9, 7.3, 7.4, 7.0, 7.1),   # 3/10 - 回撤到7.0，应该确认前面的HIGH
        (10, 7.1, 7.3, 6.9, 7.2),  # 3/11 - 最低点6.9
        (11, 7.2, 7.4, 7.1, 7.3),  # 3/12 - 开始反弹
        (12, 7.3, 7.5, 7.2, 7.4),  # 3/13
        (13, 7.4, 7.6, 7.3, 7.5),  # 3/14
        (14, 7.5, 7.7, 7.4, 7.6),  # 3/15
        (15, 7.6, 7.8, 7.5, 7.7),  # 3/16
        (16, 7.7, 8.0, 7.6, 7.9),  # 3/17 - 新的高点，可能的第二个HIGH
        (17, 7.9, 8.1, 7.8, 8.0),  # 3/18 - 继续创新高
    ]
    
    for i, (day_offset, open_price, high_price, low_price, close_price) in enumerate(prices):
        date = base_date + timedelta(days=day_offset)
        stock_data = StockData(
            symbol="TEST",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000  # 固定成交量
        )
        test_data.append(stock_data)
    
    return test_data

def test_zigzag_algorithm():
    """测试ZigZag算法"""
    print("=" * 60)
    print("测试修复后的 ZigZag 算法")
    print("=" * 60)
    
    # 创建测试数据
    test_data = create_test_data()
    print(f"创建了 {len(test_data)} 个测试数据点")
    
    # 创建分析器
    analyzer = ZigZagAnalyzer(
        base_threshold=0.05,  # 5% 阈值
        dynamic_threshold=False,  # 使用固定阈值便于测试
        trend_lookback_points=4
    )
    
    # 执行分析
    analyzed_data = analyzer.calculate_and_analyze(test_data)
    
    # 提取ZigZag点
    zigzag_points = []
    for i, data in enumerate(analyzed_data):
        if data.is_zigzag_point:
            zigzag_points.append({
                'index': i,
                'date': data.date.strftime('%Y-%m-%d'),
                'type': data.zigzag_point_type,
                'price': float(data.high if data.zigzag_point_type == 'HIGH' else data.low),
                'close': float(data.close)
            })
    
    print(f"\n发现 {len(zigzag_points)} 个 ZigZag 点:")
    print("-" * 60)
    
    for i, point in enumerate(zigzag_points):
        print(f"{i+1:2d}. {point['date']} | {point['type']:4s} | "
              f"价格: {point['price']:6.2f} | 收盘: {point['close']:6.2f}")
    
    # 检查连续性
    print("\n" + "=" * 60)
    print("连续性检查:")
    print("=" * 60)
    
    consecutive_issues = []
    for i in range(len(zigzag_points) - 1):
        current = zigzag_points[i]
        next_point = zigzag_points[i + 1]
        
        if current['type'] == next_point['type']:
            consecutive_issues.append((current, next_point))
    
    if consecutive_issues:
        print(f"❌ 发现 {len(consecutive_issues)} 组连续的同类型点:")
        for i, (p1, p2) in enumerate(consecutive_issues):
            print(f"   {i+1}. {p1['date']} ({p1['type']}: {p1['price']:.2f}) -> "
                  f"{p2['date']} ({p2['type']}: {p2['price']:.2f})")
    else:
        print("✅ 没有连续的同类型点，ZigZag 序列正确")
    
    # 显示详细的价格走势
    print("\n" + "=" * 60)
    print("详细价格走势:")
    print("=" * 60)
    
    for i, data in enumerate(analyzed_data):
        marker = ""
        if data.is_zigzag_point:
            marker = f" <- {data.zigzag_point_type}"
        
        print(f"{data.date.strftime('%Y-%m-%d')} | "
              f"开:{float(data.open):5.2f} 高:{float(data.high):5.2f} "
              f"低:{float(data.low):5.2f} 收:{float(data.close):5.2f}{marker}")

if __name__ == "__main__":
    test_zigzag_algorithm()
