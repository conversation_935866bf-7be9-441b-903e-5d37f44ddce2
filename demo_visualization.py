#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票ZigZag分析可视化演示脚本

这个脚本演示如何使用stock_visualization模块创建专业的股票分析图表
包含K线图、成交量、峰谷点标记、趋势状态等功能
"""

from stock_visualization import visualize_stock_analysis, StockVisualizer
from zigzag_analysis import ZigZagAnalyzer, get_stock_data, dataframe_to_stock_data
import webbrowser
import os


def demo_single_stock():
    """演示单个股票的分析和可视化"""
    print("=" * 60)
    print("股票ZigZag分析可视化演示")
    print("=" * 60)
    
    # 配置参数
    stock_code = "600000"  # 浦发银行
    start_date = "20230101"
    end_date = "20240101"
    base_threshold = 0.05  # 5%的ZigZag阈值
    
    print(f"股票代码: {stock_code}")
    print(f"分析期间: {start_date} - {end_date}")
    print(f"ZigZag阈值: {base_threshold * 100}%")
    print("-" * 60)
    try:
        # 执行完整的分析和可视化
        html_file = visualize_stock_analysis(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            base_threshold=base_threshold,
            output_file=f"{stock_code}_专业分析图表.html"
        )
        
        print(f"\n✅ 分析完成！")
        print(f"📊 图表文件: {html_file}")
        
        # 询问是否打开浏览器
        if input("\n是否在浏览器中打开图表？(y/n): ").strip().lower() == 'y':
            webbrowser.open(f'file://{os.path.abspath(html_file)}')
            print("🌐 已在浏览器中打开图表")
        
        return html_file
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return None


def demo_custom_analysis():
    """演示自定义参数的分析"""
    print("\n" + "=" * 60)
    print("自定义参数分析演示")
    print("=" * 60)
    
    # 获取用户输入
    stock_code = input("请输入股票代码 (默认: 000001): ").strip() or "000001"
    start_date = input("请输入开始日期 YYYYMMDD (默认: 20230601): ").strip() or "20230601"
    end_date = input("请输入结束日期 YYYYMMDD (默认: 20240601): ").strip() or "20240601"
    
    try:
        threshold_input = input("请输入ZigZag阈值 0-1 (默认: 0.03): ").strip()
        base_threshold = float(threshold_input) if threshold_input else 0.03
    except ValueError:
        base_threshold = 0.03
        print("⚠️ 阈值输入无效，使用默认值 0.03")
    
    print(f"\n开始分析 {stock_code}...")
    
    try:
        # 获取数据
        df = get_stock_data(stock_code, start_date, end_date)
        stock_data_list = dataframe_to_stock_data(df, stock_code)
        
        # 创建分析器
        analyzer = ZigZagAnalyzer(
            base_threshold=base_threshold,
            dynamic_threshold=True,
            trend_lookback_points=4
        )
        
        # 执行分析
        analyzed_data = analyzer.calculate_and_analyze(stock_data_list)
        
        # 创建可视化
        visualizer = StockVisualizer(width="100%", height="900px")
        output_file = f"{stock_code}_自定义分析_{start_date}_{end_date}.html"
        
        filename = visualizer.save_chart(analyzed_data, output_file, stock_code)
        
        # 显示统计信息
        zigzag_points = [data for data in analyzed_data if data.is_zigzag_point]
        trend_data = [data for data in analyzed_data if data.trend_status]
        
        print(f"\n📈 分析结果:")
        print(f"   总交易日: {len(analyzed_data)}")
        print(f"   峰谷点数: {len(zigzag_points)}")
        print(f"   有趋势判断的交易日: {len(trend_data)}")
        
        if trend_data:
            latest_trend = trend_data[-1].trend_status
            print(f"   最新趋势状态: {latest_trend}")
        
        print(f"\n📊 图表已保存: {filename}")
        
        # 询问是否打开
        if input("\n是否在浏览器中打开图表？(y/n): ").lower() == 'y':
            webbrowser.open(f'file://{os.path.abspath(filename)}')
        
        return filename
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return None


def demo_multiple_stocks():
    """演示多个股票的批量分析"""
    print("\n" + "=" * 60)
    print("批量股票分析演示")
    print("=" * 60)
    
    # 预定义的股票列表
    stocks = [
        ("000001", "平安银行"),
        ("000002", "万科A"),
        ("600000", "浦发银行"),
        ("600036", "招商银行"),
        ("000858", "五粮液")
    ]
    
    start_date = "20230801"
    end_date = "20240801"
    base_threshold = 0.04
    
    print(f"分析期间: {start_date} - {end_date}")
    print(f"ZigZag阈值: {base_threshold * 100}%")
    print(f"股票列表: {', '.join([f'{code}({name})' for code, name in stocks])}")
    print("-" * 60)
    
    results = []
    
    for i, (stock_code, stock_name) in enumerate(stocks, 1):
        print(f"\n[{i}/{len(stocks)}] 正在分析 {stock_code} ({stock_name})...")
        
        try:
            html_file = visualize_stock_analysis(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                base_threshold=base_threshold,
                output_file=f"{stock_code}_{stock_name}_批量分析.html"
            )
            
            results.append((stock_code, stock_name, html_file, "成功"))
            print(f"✅ {stock_name} 分析完成")
            
        except Exception as e:
            results.append((stock_code, stock_name, None, f"失败: {str(e)}"))
            print(f"❌ {stock_name} 分析失败: {str(e)}")
    
    # 显示汇总结果
    print("\n" + "=" * 60)
    print("批量分析结果汇总")
    print("=" * 60)
    
    for stock_code, stock_name, html_file, status in results:
        print(f"{stock_code} ({stock_name}): {status}")
        if html_file:
            print(f"   📊 图表文件: {html_file}")
    
    successful_files = [result[2] for result in results if result[2]]
    
    if successful_files:
        print(f"\n✅ 成功生成 {len(successful_files)} 个图表文件")
        
        if input("\n是否打开所有成功的图表？(y/n): ").lower() == 'y':
            for html_file in successful_files:
                webbrowser.open(f'file://{os.path.abspath(html_file)}')
            print("🌐 已在浏览器中打开所有图表")
    
    return results


def main():
    """主函数 - 演示菜单"""
    while True:
        print("\n" + "=" * 60)
        print("股票ZigZag分析可视化演示系统")
        print("=" * 60)
        print("1. 单股票演示 (浦发银行)")
        print("2. 自定义参数分析")
        print("3. 批量股票分析")
        print("4. 退出")
        print("-" * 60)
        
        choice = input("请选择功能 (1-4): ").strip()
        
        if choice == '1':
            demo_single_stock()
        elif choice == '2':
            demo_custom_analysis()
        elif choice == '3':
            demo_multiple_stocks()
        elif choice == '4':
            print("👋 感谢使用！")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")


if __name__ == "__main__":
    print("🚀 启动股票ZigZag分析可视化演示系统...")
    print("📋 功能特点:")
    print("   • 专业K线图 + 成交量图表")
    print("   • 十字线跟随鼠标，贯穿K线和成交量")
    print("   • 峰谷点标记和ZigZag趋势线")
    print("   • 鼠标悬停显示详细信息和趋势状态")
    print("   • 支持缩放和数据筛选")
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
