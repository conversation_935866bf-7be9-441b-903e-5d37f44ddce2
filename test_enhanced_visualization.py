#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强后的可视化功能
创建一个有连续 HIGH 点问题的数据集，验证新的验证面板是否正常工作
"""

import pandas as pd
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from zigzag_analysis import StockData, ZigZagAnalyzer
from stock_visualization import StockVisualizer

def create_problematic_data():
    """创建一个会产生连续 HIGH 点的数据集"""
    base_date = datetime(2024, 3, 1)
    stock_data = []
    
    # 模拟数据：故意创造连续 HIGH 的场景
    prices = [
        # 日期偏移, 开盘, 最高, 最低, 收盘
        (0, 7.0, 7.1, 6.9, 7.05),   # 3/1
        (1, 7.05, 7.2, 7.0, 7.15),  # 3/2
        (2, 7.15, 7.3, 7.1, 7.25),  # 3/3
        (3, 7.25, 7.4, 7.2, 7.35),  # 3/4
        (4, 7.35, 7.5, 7.3, 7.45),  # 3/5
        (5, 7.45, 7.6, 7.4, 7.55),  # 3/6
        (6, 7.55, 7.7, 7.5, 7.65),  # 3/7
        (7, 7.65, 7.8, 7.6, 7.75),  # 3/8
        (8, 7.75, 7.9, 7.7, 7.85),  # 3/9
        (9, 7.85, 8.0, 7.8, 7.95),  # 3/10
        (10, 7.95, 8.1, 7.9, 8.05), # 3/11
        (11, 8.05, 8.2, 8.0, 8.15), # 3/12 - 第一个 HIGH 点
        
        # 小幅回撤，但不够深，不会触发 LOW 确认
        (12, 8.15, 8.1, 7.95, 8.0), # 3/13 - 回撤到 7.95，约 3% 回撤
        (13, 8.0, 8.05, 7.9, 7.95), # 3/14 - 继续小幅波动
        (14, 7.95, 8.0, 7.85, 7.9), # 3/15 - 最低到 7.85，约 4.3% 回撤（可能不够 5%）
        
        # 然后快速反弹并创新高
        (15, 7.9, 7.95, 7.85, 7.92), # 3/16
        (16, 7.92, 8.0, 7.9, 7.98),  # 3/17
        (17, 7.98, 8.1, 7.95, 8.05), # 3/18
        (18, 8.05, 8.2, 8.0, 8.15),  # 3/19
        (19, 8.15, 8.3, 8.1, 8.25),  # 3/20
        (20, 8.25, 8.4, 8.2, 8.35),  # 3/21
        (21, 8.35, 8.5, 8.3, 8.45),  # 3/22
        (22, 8.45, 8.6, 8.4, 8.55),  # 3/23
        (23, 8.55, 8.7, 8.5, 8.65),  # 3/24
        (24, 8.65, 8.8, 8.6, 8.75),  # 3/25
        (25, 8.75, 8.9, 8.7, 8.85),  # 3/26
        (26, 8.85, 9.0, 8.8, 8.95),  # 3/27
        (27, 8.95, 9.1, 8.9, 9.05),  # 3/28
        (28, 9.05, 9.2, 9.0, 9.15),  # 3/29
        (29, 9.15, 9.3, 9.1, 9.25),  # 3/30
        (30, 9.25, 9.4, 9.2, 9.35),  # 3/31
        
        # 4月继续上升
        (31, 9.35, 9.5, 9.3, 9.45),  # 4/1
        (32, 9.45, 9.6, 9.4, 9.55),  # 4/2
        (33, 9.55, 9.7, 9.5, 9.65),  # 4/3
        (34, 9.65, 9.8, 9.6, 9.75),  # 4/4
        (35, 9.75, 9.9, 9.7, 9.85),  # 4/5
        (36, 9.85, 10.0, 9.8, 9.95), # 4/6
        (37, 9.95, 10.1, 9.9, 10.05),# 4/7
        (38, 10.05, 10.2, 10.0, 10.15), # 4/8
        (39, 10.15, 10.3, 10.1, 10.25), # 4/9
        (40, 10.25, 10.4, 10.2, 10.35), # 4/10 - 第二个 HIGH 点
        
        # 然后大幅回撤
        (41, 10.35, 10.3, 9.8, 9.9),  # 4/11 - 大幅回撤
        (42, 9.9, 9.95, 9.5, 9.6),    # 4/12 - 继续下跌
        (43, 9.6, 9.7, 9.3, 9.4),     # 4/13
        (44, 9.4, 9.5, 9.1, 9.2),     # 4/14
        (45, 9.2, 9.3, 8.9, 9.0),     # 4/15 - 最低点
    ]
    
    for day_offset, open_price, high_price, low_price, close_price in prices:
        date = base_date + timedelta(days=day_offset)
        stock_data.append(StockData(
            symbol="TEST_PROBLEM",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        ))
    
    return stock_data

def test_enhanced_visualization():
    """测试增强后的可视化功能"""
    print("=" * 80)
    print("测试增强后的可视化功能")
    print("=" * 80)
    
    # 创建有问题的数据
    stock_data = create_problematic_data()
    print(f"创建了 {len(stock_data)} 个数据点")
    
    # 执行 ZigZag 分析
    analyzer = ZigZagAnalyzer(
        base_threshold=0.05,  # 5% 阈值
        dynamic_threshold=False,  # 使用固定阈值便于测试
        trend_lookback_points=4
    )
    
    analyzed_data = analyzer.calculate_and_analyze(stock_data)
    
    # 创建可视化
    visualizer = StockVisualizer()
    output_file = "test_enhanced_zigzag_validation.html"
    
    filename = visualizer.save_chart(analyzed_data, output_file, "TEST_PROBLEM")
    
    # 显示 ZigZag 点信息
    zigzag_points = [data for data in analyzed_data if data.is_zigzag_point]
    print(f"\n发现 {len(zigzag_points)} 个 ZigZag 点:")
    print("-" * 80)
    
    for i, data in enumerate(zigzag_points):
        price = float(data.high if data.zigzag_point_type == 'HIGH' else data.low)
        print(f"{i+1:2d}. {data.date.strftime('%Y-%m-%d')} | {data.zigzag_point_type:4s} | "
              f"价格: {price:6.2f} | 收盘: {float(data.close):6.2f}")
    
    print(f"\n✅ 增强版可视化已生成: {filename}")
    print("📊 请打开 HTML 文件查看:")
    print(f"   - 如果存在连续的同类型 ZigZag 点，右上角会显示黄色警告面板")
    print(f"   - 面板会显示具体的问题详情和建议的缺失点")
    print(f"   - 可以点击 × 关闭警告面板")
    
    return filename

if __name__ == "__main__":
    test_enhanced_visualization()
