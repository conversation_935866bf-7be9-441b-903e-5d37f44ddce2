#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的 ZigZag 验证功能
使用模拟数据验证算法是否能正确标记局部极值
"""

from dataclasses import dataclass
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional, List
import random

@dataclass
class StockData:
    symbol: str
    date: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    vol5: Optional[Decimal] = None
    vol20: Optional[Decimal] = None
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None
    trend_status: Optional[str] = None

def calculate_atr(data: List[StockData], period: int = 14) -> List[Optional[Decimal]]:
    atr_values = [None] * len(data)
    if not data:
        return atr_values

    trs: List[Decimal] = []
    running_sum = Decimal('0')

    for i in range(len(data)):
        if i == 0:
            tr = data[0].high - data[0].low
        else:
            prev_close = data[i-1].close
            tr = max(
                data[i].high - data[i].low,
                abs(data[i].high - prev_close),
                abs(data[i].low - prev_close)
            )
        trs.append(tr)
        running_sum += tr

        if i >= period:
            running_sum -= trs[i - period]
            atr_values[i] = running_sum / Decimal(period)

    return atr_values

def calculate_zigzag_fixed(
    data: List[StockData],
    atr_multiplier: Decimal = Decimal('1.5'),
    atr_period: int = 14,
    min_price_move: Decimal = Decimal('0'),
    min_trend_bars: int = 1
):
    """
    修正后的ZigZag算法 - 确保标记的点是真正的局部极值
    """
    if not data:
        return

    # 清除之前的标记
    for d in data:
        d.is_zigzag_point = False
        d.zigzag_point_type = None

    atr_values = calculate_atr(data, atr_period)
    
    # 存储所有潜在的转折点
    potential_pivots = []
    
    # 第一步：使用原始算法找到转折信号
    high_val = data[0].high
    high_idx = 0
    low_val = data[0].low
    low_idx = 0
    trend: Optional[str] = None
    
    print(f"开始计算ZigZag，数据长度: {len(data)}")

    for i in range(1, len(data)):
        current_bar = data[i]
        atr = atr_values[i]

        # 始终更新极值
        if current_bar.high > high_val:
            high_val = current_bar.high
            high_idx = i
        if current_bar.low < low_val:
            low_val = current_bar.low
            low_idx = i

        if atr is None:
            continue

        threshold = atr * atr_multiplier

        # 初始趋势判断
        if trend is None:
            swing_range = high_val - low_val
            if swing_range >= threshold and swing_range >= min_price_move:
                if high_idx > low_idx:
                    potential_pivots.append((low_idx, 'LOW'))
                    trend = 'UP'
                else:
                    potential_pivots.append((high_idx, 'HIGH'))
                    trend = 'DOWN'
            continue

        # 趋势反转检测
        bars_since_extreme = i - max(high_idx, low_idx)
        
        if trend == 'UP':
            pullback = high_val - current_bar.low
            if (pullback >= threshold and 
                pullback >= min_price_move and 
                bars_since_extreme >= min_trend_bars):
                
                potential_pivots.append((high_idx, 'HIGH'))
                trend = 'DOWN'
                # 重置极值跟踪
                high_val = current_bar.high
                high_idx = i
                low_val = current_bar.low
                low_idx = i

        elif trend == 'DOWN':
            bounce = current_bar.high - low_val
            if (bounce >= threshold and 
                bounce >= min_price_move and 
                bars_since_extreme >= min_trend_bars):
                
                potential_pivots.append((low_idx, 'LOW'))
                trend = 'UP'
                # 重置极值跟踪
                high_val = current_bar.high
                high_idx = i
                low_val = current_bar.low
                low_idx = i

    # 添加最后一个极值
    if trend == 'UP':
        potential_pivots.append((high_idx, 'HIGH'))
    elif trend == 'DOWN':
        potential_pivots.append((low_idx, 'LOW'))

    # 第二步：验证并修正每个转折点，确保它们是真正的局部极值
    window = 5  # 验证窗口大小
    
    print(f"发现{len(potential_pivots)}个潜在pivot点，开始验证...")
    
    for pivot_idx, pivot_type in potential_pivots:
        start_idx = max(0, pivot_idx - window)
        end_idx = min(len(data), pivot_idx + window + 1)
        
        if pivot_type == 'HIGH':
            # 在窗口内找到真正的最高点
            window_highs = [(j, data[j].high) for j in range(start_idx, end_idx)]
            true_high_idx, true_high_val = max(window_highs, key=lambda x: x[1])
            
            data[true_high_idx].is_zigzag_point = True
            data[true_high_idx].zigzag_point_type = 'HIGH'
            
            if true_high_idx != pivot_idx:
                print(f"修正HIGH点：从索引{pivot_idx}({data[pivot_idx].high})修正到索引{true_high_idx}({true_high_val})")
            else:
                print(f"确认HIGH点：索引{pivot_idx}，价格{true_high_val}")
                
        elif pivot_type == 'LOW':
            # 在窗口内找到真正的最低点
            window_lows = [(j, data[j].low) for j in range(start_idx, end_idx)]
            true_low_idx, true_low_val = min(window_lows, key=lambda x: x[1])
            
            data[true_low_idx].is_zigzag_point = True
            data[true_low_idx].zigzag_point_type = 'LOW'
            
            if true_low_idx != pivot_idx:
                print(f"修正LOW点：从索引{pivot_idx}({data[pivot_idx].low})修正到索引{true_low_idx}({true_low_val})")
            else:
                print(f"确认LOW点：索引{pivot_idx}，价格{true_low_val}")

    # 验证结果
    final_pivots = [(i, d.zigzag_point_type, d.high if d.zigzag_point_type == 'HIGH' else d.low) 
                    for i, d in enumerate(data) if d.is_zigzag_point]
    print(f"最终确认{len(final_pivots)}个pivot点:")
    for idx, ptype, price in final_pivots:
        print(f"  索引{idx}: {ptype} = {price}")

def validate_zigzag_points(data: List[StockData], window: int = 5):
    """
    验证ZigZag点是否确实是局部极值
    """
    print("\n=== 验证ZigZag点 ===")
    all_valid = True
    
    for i, point in enumerate(data):
        if point.is_zigzag_point:
            # 检查窗口范围内是否确实是极值
            start_idx = max(0, i - window)
            end_idx = min(len(data), i + window + 1)
            
            if point.zigzag_point_type == 'HIGH':
                window_highs = [data[j].high for j in range(start_idx, end_idx)]
                is_valid = point.high == max(window_highs)
                status = '✓' if is_valid else '✗'
                print(f"索引{i} HIGH点 {point.high}: {status} "
                      f"(窗口{start_idx}-{end_idx-1}最高{max(window_highs)})")
                if not is_valid:
                    all_valid = False
                    
            elif point.zigzag_point_type == 'LOW':
                window_lows = [data[j].low for j in range(start_idx, end_idx)]
                is_valid = point.low == min(window_lows)
                status = '✓' if is_valid else '✗'
                print(f"索引{i} LOW点 {point.low}: {status} "
                      f"(窗口{start_idx}-{end_idx-1}最低{min(window_lows)})")
                if not is_valid:
                    all_valid = False
    
    print(f"\n验证结果: {'✅ 所有点都是有效的局部极值' if all_valid else '❌ 存在无效的标记点'}")
    return all_valid

def create_test_data():
    """创建测试数据"""
    base_date = datetime(2023, 1, 1)
    data = []
    
    # 创建一个有明显波动的价格序列
    base_price = 10.0
    prices = []
    
    # 生成价格序列：上升-下降-上升-下降的模式
    for i in range(100):
        if i < 20:  # 上升
            price = base_price + i * 0.1 + random.uniform(-0.05, 0.05)
        elif i < 40:  # 下降
            price = base_price + 2.0 - (i-20) * 0.15 + random.uniform(-0.05, 0.05)
        elif i < 70:  # 上升
            price = base_price - 1.0 + (i-40) * 0.12 + random.uniform(-0.05, 0.05)
        else:  # 下降
            price = base_price + 2.6 - (i-70) * 0.08 + random.uniform(-0.05, 0.05)
        
        prices.append(max(1.0, price))  # 确保价格为正
    
    # 创建 StockData 对象
    for i, price in enumerate(prices):
        date = base_date + timedelta(days=i)
        
        # 创建 OHLC 数据
        open_price = price + random.uniform(-0.02, 0.02)
        close_price = price + random.uniform(-0.02, 0.02)
        high_price = max(open_price, close_price) + random.uniform(0, 0.05)
        low_price = min(open_price, close_price) - random.uniform(0, 0.05)
        
        data.append(StockData(
            symbol="TEST",
            date=date,
            open=Decimal(str(round(open_price, 2))),
            high=Decimal(str(round(high_price, 2))),
            low=Decimal(str(round(low_price, 2))),
            close=Decimal(str(round(close_price, 2))),
            volume=1000000
        ))
    
    return data

def main():
    print("=" * 60)
    print("测试修复后的 ZigZag 验证功能")
    print("=" * 60)
    
    # 创建测试数据
    test_data = create_test_data()
    print(f"创建了 {len(test_data)} 个测试数据点")
    
    # 运行修复后的 ZigZag 算法
    calculate_zigzag_fixed(test_data, atr_multiplier=Decimal('2.0'))
    
    # 验证结果
    print("\n" + "=" * 60)
    is_valid = validate_zigzag_points(test_data)
    
    if is_valid:
        print("\n🎉 修复成功！所有 ZigZag 点都是有效的局部极值")
    else:
        print("\n⚠️  仍有问题，需要进一步调整算法")

if __name__ == "__main__":
    main()
